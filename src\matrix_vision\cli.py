"""Command line interface for Matrix Vision."""

import asyncio
import sys
from pathlib import Path
from typing import Optional

import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

from .core.config import settings
from .core.logger import setup_logging, get_logger
from .mcp.mcp_server import MatrixVisionMCPServer
from .server.websocket_server import MatrixVisionServer
from .streaming.screen_streamer import ScreenStreamer
from .capture.screen_capture import ScreenCapture

console = Console()
logger = get_logger(__name__)


@click.group()
@click.option('--debug', is_flag=True, help='Enable debug mode')
@click.option('--config', type=click.Path(), help='Configuration file path')
@click.version_option(version='0.1.0', prog_name='Matrix Vision')
def cli(debug: bool, config: Optional[str]):
    """Matrix Vision - AI-powered screen streaming system."""
    if debug:
        settings.debug = True
        settings.logging.level = "DEBUG"
    
    if config:
        # Load custom config if provided
        config_path = Path(config)
        if config_path.exists():
            console.print(f"[green]Loading config from: {config_path}[/green]")
        else:
            console.print(f"[red]Config file not found: {config_path}[/red]")
            sys.exit(1)
    
    # Setup logging
    setup_logging()


@cli.command()
@click.option('--host', default='localhost', help='Server host')
@click.option('--port', default=8765, help='Server port')
@click.option('--max-connections', default=100, help='Maximum connections')
def server(host: str, port: int, max_connections: int):
    """Start the WebSocket server."""
    console.print(Panel.fit(
        "[bold blue]Matrix Vision WebSocket Server[/bold blue]\n"
        f"Host: {host}\n"
        f"Port: {port}\n"
        f"Max Connections: {max_connections}",
        title="Server Configuration"
    ))
    
    # Update settings
    settings.server.host = host
    settings.server.port = port
    settings.server.max_connections = max_connections
    
    async def run_server():
        server_instance = MatrixVisionServer()
        try:
            await server_instance.start()
        except KeyboardInterrupt:
            console.print("\n[yellow]Server stopped by user[/yellow]")
        except Exception as e:
            console.print(f"[red]Server error: {e}[/red]")
        finally:
            await server_instance.cleanup()
    
    asyncio.run(run_server())


@cli.command()
def mcp():
    """Start the MCP (Model Context Protocol) server."""
    console.print(Panel.fit(
        "[bold green]Matrix Vision MCP Server[/bold green]\n"
        "Connecting AI models to screen streaming...\n"
        "Use stdio transport for communication.",
        title="MCP Server"
    ))
    
    async def run_mcp():
        mcp_server = MatrixVisionMCPServer()
        try:
            await mcp_server.start()
        except KeyboardInterrupt:
            console.print("\n[yellow]MCP Server stopped by user[/yellow]")
        except Exception as e:
            console.print(f"[red]MCP Server error: {e}[/red]")
        finally:
            await mcp_server.cleanup()
    
    asyncio.run(run_mcp())


@cli.command()
@click.option('--monitor', default=0, help='Monitor index to capture')
@click.option('--fps', default=10, help='Frames per second')
@click.option('--format', default='base64_jpeg', 
              type=click.Choice(['base64_jpeg', 'base64_png', 'base64_webp']),
              help='Output format')
@click.option('--quality', default=85, help='Image quality (1-100)')
@click.option('--output', type=click.Path(), help='Output file for single capture')
def capture(monitor: int, fps: int, format: str, quality: int, output: Optional[str]):
    """Capture screen content."""
    
    async def run_capture():
        screen_capture = ScreenCapture()
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task("Initializing screen capture...", total=None)
                
                await screen_capture.initialize()
                progress.update(task, description="Capturing screen...")
                
                if output:
                    # Single capture
                    base64_data, metadata = await screen_capture.capture_frame_base64(
                        monitor=monitor, format_output=format.upper().replace('BASE64_', '')
                    )
                    
                    # Save to file
                    import base64
                    output_path = Path(output)
                    output_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    with open(output_path, 'wb') as f:
                        f.write(base64.b64decode(base64_data))
                    
                    progress.update(task, description=f"Saved to {output_path}")
                    
                    # Show metadata
                    table = Table(title="Capture Metadata")
                    table.add_column("Property", style="cyan")
                    table.add_column("Value", style="green")
                    
                    for key, value in metadata.items():
                        table.add_row(str(key), str(value))
                    
                    console.print(table)
                else:
                    # Continuous capture (demo)
                    console.print(f"[green]Starting continuous capture at {fps} FPS...[/green]")
                    console.print("[yellow]Press Ctrl+C to stop[/yellow]")
                    
                    frame_count = 0
                    while True:
                        frame, metadata = await screen_capture.capture_frame(monitor=monitor)
                        frame_count += 1
                        
                        progress.update(
                            task, 
                            description=f"Captured {frame_count} frames | "
                                      f"Size: {frame.shape[1]}x{frame.shape[0]} | "
                                      f"FPS: {metadata.get('fps', 'N/A')}"
                        )
                        
                        await asyncio.sleep(1.0 / fps)
                        
        except KeyboardInterrupt:
            console.print("\n[yellow]Capture stopped by user[/yellow]")
        except Exception as e:
            console.print(f"[red]Capture error: {e}[/red]")
        finally:
            await screen_capture.cleanup()
    
    asyncio.run(run_capture())


@cli.command()
def monitors():
    """List available monitors."""
    
    async def list_monitors():
        screen_capture = ScreenCapture()
        
        try:
            await screen_capture.initialize()
            monitors = await screen_capture.get_monitors()
            
            table = Table(title="Available Monitors")
            table.add_column("Index", style="cyan")
            table.add_column("Width", style="green")
            table.add_column("Height", style="green")
            table.add_column("Left", style="yellow")
            table.add_column("Top", style="yellow")
            
            for i, monitor in enumerate(monitors):
                table.add_row(
                    str(i),
                    str(monitor.get('width', 'N/A')),
                    str(monitor.get('height', 'N/A')),
                    str(monitor.get('left', 'N/A')),
                    str(monitor.get('top', 'N/A'))
                )
            
            console.print(table)
            
        except Exception as e:
            console.print(f"[red]Error listing monitors: {e}[/red]")
        finally:
            await screen_capture.cleanup()
    
    asyncio.run(list_monitors())


@cli.command()
@click.option('--session-id', required=True, help='Streaming session ID')
@click.option('--client-type', default='claude', help='AI client type')
@click.option('--fps', default=5, help='Frames per second')
@click.option('--monitor', default=0, help='Monitor to capture')
def stream(session_id: str, client_type: str, fps: int, monitor: int):
    """Start AI vision streaming."""
    
    async def run_streaming():
        streamer = ScreenStreamer()
        
        try:
            console.print(Panel.fit(
                f"[bold blue]AI Vision Streaming[/bold blue]\n"
                f"Session ID: {session_id}\n"
                f"Client Type: {client_type}\n"
                f"FPS: {fps}\n"
                f"Monitor: {monitor}",
                title="Streaming Configuration"
            ))
            
            # Start streaming
            await streamer.start_streaming(monitor=monitor, fps=fps)
            
            # Subscribe a demo callback
            async def demo_callback(frame_data):
                console.print(f"[green]Frame received: {len(frame_data['data'])} bytes[/green]")
            
            await streamer.subscribe(session_id, demo_callback)
            
            console.print("[green]Streaming started. Press Ctrl+C to stop.[/green]")
            
            # Keep running
            while True:
                await asyncio.sleep(1)
                stats = streamer.get_stats()
                console.print(f"Frames streamed: {stats['frames_streamed']}, FPS: {stats['avg_fps']:.1f}")
                
        except KeyboardInterrupt:
            console.print("\n[yellow]Streaming stopped by user[/yellow]")
        except Exception as e:
            console.print(f"[red]Streaming error: {e}[/red]")
        finally:
            await streamer.cleanup()
    
    asyncio.run(run_streaming())


@cli.command()
def status():
    """Show system status and configuration."""
    
    table = Table(title="Matrix Vision Status")
    table.add_column("Component", style="cyan")
    table.add_column("Status", style="green")
    table.add_column("Details", style="yellow")
    
    # Check dependencies
    try:
        import mss
        table.add_row("MSS (Screen Capture)", "✓ Available", f"Version: {mss.__version__}")
    except ImportError:
        table.add_row("MSS (Screen Capture)", "✗ Missing", "pip install mss")
    
    try:
        import PIL
        table.add_row("PIL (Image Processing)", "✓ Available", f"Version: {PIL.__version__}")
    except ImportError:
        table.add_row("PIL (Image Processing)", "✗ Missing", "pip install Pillow")
    
    try:
        import cv2
        table.add_row("OpenCV", "✓ Available", f"Version: {cv2.__version__}")
    except ImportError:
        table.add_row("OpenCV", "✗ Optional", "pip install opencv-python")
    
    try:
        import fastapi
        table.add_row("FastAPI", "✓ Available", f"Version: {fastapi.__version__}")
    except ImportError:
        table.add_row("FastAPI", "✗ Missing", "pip install fastapi")
    
    # Configuration
    table.add_row("", "", "")  # Separator
    table.add_row("Server Host", "Config", settings.server.host)
    table.add_row("Server Port", "Config", str(settings.server.port))
    table.add_row("Target FPS", "Config", str(settings.capture.fps))
    table.add_row("Image Quality", "Config", str(settings.capture.quality))
    table.add_row("Debug Mode", "Config", str(settings.debug))
    
    console.print(table)


def main():
    """Main entry point."""
    try:
        cli()
    except KeyboardInterrupt:
        console.print("\n[yellow]Interrupted by user[/yellow]")
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        if settings.debug:
            console.print_exception()
        sys.exit(1)


if __name__ == '__main__':
    main()
