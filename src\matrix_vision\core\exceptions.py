"""Custom exceptions for Matrix Vision."""


class MatrixVisionError(Exception):
    """Base exception for Matrix Vision."""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "MATRIX_VISION_ERROR"
        self.details = details or {}
    
    def to_dict(self) -> dict:
        """Convert exception to dictionary."""
        return {
            "error": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details,
        }


class ScreenCaptureError(MatrixVisionError):
    """Exception raised during screen capture operations."""
    
    def __init__(self, message: str, monitor: int = None, region: tuple = None):
        super().__init__(message, "SCREEN_CAPTURE_ERROR")
        if monitor is not None:
            self.details["monitor"] = monitor
        if region is not None:
            self.details["region"] = region


class EffectError(MatrixVisionError):
    """Exception raised during effect processing."""
    
    def __init__(self, message: str, effect_type: str = None, parameters: dict = None):
        super().__init__(message, "EFFECT_ERROR")
        if effect_type:
            self.details["effect_type"] = effect_type
        if parameters:
            self.details["parameters"] = parameters


class WebSocketError(MatrixVisionError):
    """Exception raised during WebSocket operations."""
    
    def __init__(self, message: str, client_id: str = None, connection_info: dict = None):
        super().__init__(message, "WEBSOCKET_ERROR")
        if client_id:
            self.details["client_id"] = client_id
        if connection_info:
            self.details["connection_info"] = connection_info


class MCPError(MatrixVisionError):
    """Exception raised during MCP operations."""
    
    def __init__(self, message: str, method: str = None, request_id: str = None):
        super().__init__(message, "MCP_ERROR")
        if method:
            self.details["method"] = method
        if request_id:
            self.details["request_id"] = request_id


class ConfigurationError(MatrixVisionError):
    """Exception raised for configuration errors."""
    
    def __init__(self, message: str, config_key: str = None, config_value = None):
        super().__init__(message, "CONFIGURATION_ERROR")
        if config_key:
            self.details["config_key"] = config_key
        if config_value is not None:
            self.details["config_value"] = config_value


class PerformanceError(MatrixVisionError):
    """Exception raised for performance-related issues."""
    
    def __init__(self, message: str, metric: str = None, threshold = None, actual = None):
        super().__init__(message, "PERFORMANCE_ERROR")
        if metric:
            self.details["metric"] = metric
        if threshold is not None:
            self.details["threshold"] = threshold
        if actual is not None:
            self.details["actual"] = actual
