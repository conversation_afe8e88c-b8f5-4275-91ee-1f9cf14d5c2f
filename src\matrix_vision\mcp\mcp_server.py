"""MCP Server implementation for Matrix Vision."""

import asyncio
import json
import sys
from typing import Any, Dict, List, Optional, Union

from ..core.config import settings
from ..core.exceptions import MCP<PERSON>rror
from ..core.logger import LoggerMixin
from .schemas import (
    MCPRequest, MCPResponse, MCPNotification, MCPError as MCPErrorSchema,
    MCPErrorCode, InitializeRequest, InitializeResponse,
    ServerCapabilities, ToolCall, ToolResult
)
from .tools import ScreenCaptureTools, StreamingTools, SystemTools


class MatrixVisionMCPServer(LoggerMixin):
    """MCP Server for Matrix Vision screen streaming."""
    
    def __init__(self):
        self.initialized = False
        self.client_capabilities = None
        
        # Initialize tool handlers
        self.screen_tools = ScreenCaptureTools()
        self.streaming_tools = StreamingTools()
        self.system_tools = SystemTools()
        
        # Combine all tools
        self.all_tools = {}
        for tool_handler in [self.screen_tools, self.streaming_tools, self.system_tools]:
            for tool in tool_handler.get_tools():
                self.all_tools[tool.name] = tool_handler
        
        # Method handlers
        self.method_handlers = {
            "initialize": self._handle_initialize,
            "tools/list": self._handle_tools_list,
            "tools/call": self._handle_tools_call,
            "notifications/initialized": self._handle_initialized,
            "ping": self._handle_ping,
        }
    
    async def start(self) -> None:
        """Start the MCP server."""
        self.logger.info(
            "Matrix Vision MCP Server starting",
            version=settings.mcp.version,
            capabilities=settings.mcp.capabilities
        )
        
        # Start reading from stdin and writing to stdout
        await self._run_stdio_server()
    
    async def _run_stdio_server(self) -> None:
        """Run MCP server using stdio transport."""
        try:
            # Read from stdin line by line
            while True:
                line = await asyncio.get_event_loop().run_in_executor(
                    None, sys.stdin.readline
                )
                
                if not line:
                    break  # EOF
                
                line = line.strip()
                if not line:
                    continue
                
                # Process the message
                response = await self._process_message(line)
                
                # Send response if any
                if response:
                    print(json.dumps(response.model_dump(exclude_none=True)), flush=True)
                    
        except KeyboardInterrupt:
            self.logger.info("MCP Server stopped by user")
        except Exception as e:
            self.logger.error("MCP Server error", error=str(e))
    
    async def _process_message(self, message_str: str) -> Optional[MCPResponse]:
        """Process incoming MCP message."""
        try:
            # Parse JSON message
            message_data = json.loads(message_str)
            
            # Determine message type
            if "method" in message_data:
                if "id" in message_data:
                    # Request
                    request = MCPRequest(**message_data)
                    return await self._handle_request(request)
                else:
                    # Notification
                    notification = MCPNotification(**message_data)
                    await self._handle_notification(notification)
                    return None
            else:
                # Invalid message
                return MCPResponse(
                    id=message_data.get("id"),
                    error=MCPErrorSchema(
                        code=MCPErrorCode.INVALID_REQUEST,
                        message="Invalid message format"
                    )
                )
                
        except json.JSONDecodeError:
            return MCPResponse(
                error=MCPErrorSchema(
                    code=MCPErrorCode.PARSE_ERROR,
                    message="Invalid JSON"
                )
            )
        except Exception as e:
            self.logger.error("Message processing error", error=str(e))
            return MCPResponse(
                error=MCPErrorSchema(
                    code=MCPErrorCode.INTERNAL_ERROR,
                    message=f"Internal error: {e}"
                )
            )
    
    async def _handle_request(self, request: MCPRequest) -> MCPResponse:
        """Handle MCP request."""
        method = request.method
        
        if method not in self.method_handlers:
            return MCPResponse(
                id=request.id,
                error=MCPErrorSchema(
                    code=MCPErrorCode.METHOD_NOT_FOUND,
                    message=f"Method not found: {method}"
                )
            )
        
        try:
            handler = self.method_handlers[method]
            result = await handler(request.params or {})
            
            return MCPResponse(
                id=request.id,
                result=result
            )
            
        except Exception as e:
            self.logger.error(
                "Request handling error",
                method=method,
                error=str(e)
            )
            return MCPResponse(
                id=request.id,
                error=MCPErrorSchema(
                    code=MCPErrorCode.INTERNAL_ERROR,
                    message=f"Request handling failed: {e}"
                )
            )
    
    async def _handle_notification(self, notification: MCPNotification) -> None:
        """Handle MCP notification."""
        method = notification.method
        
        if method in self.method_handlers:
            try:
                handler = self.method_handlers[method]
                await handler(notification.params or {})
            except Exception as e:
                self.logger.error(
                    "Notification handling error",
                    method=method,
                    error=str(e)
                )
    
    async def _handle_initialize(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Handle initialize request."""
        try:
            init_request = InitializeRequest(**params)
            self.client_capabilities = init_request.capabilities
            
            # Create response
            response = InitializeResponse(
                capabilities=ServerCapabilities(),
                server_info={
                    "name": "matrix-vision",
                    "version": "0.1.0",
                    "description": "AI-powered screen streaming with Matrix Vision"
                }
            )
            
            self.logger.info(
                "Client initialized",
                client_info=init_request.client_info,
                protocol_version=init_request.protocol_version
            )
            
            return response.model_dump()
            
        except Exception as e:
            raise MCPError(f"Initialize failed: {e}")
    
    async def _handle_initialized(self, params: Dict[str, Any]) -> None:
        """Handle initialized notification."""
        self.initialized = True
        self.logger.info("MCP Server initialized")
    
    async def _handle_tools_list(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Handle tools list request."""
        tools = []
        
        for tool_handler in [self.screen_tools, self.streaming_tools, self.system_tools]:
            for tool_def in tool_handler.get_tools():
                tools.append({
                    "name": tool_def.name,
                    "description": tool_def.description,
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            param.name: {
                                "type": param.type,
                                "description": param.description,
                                **({"default": param.default} if param.default is not None else {}),
                                **({"enum": param.enum} if param.enum else {})
                            }
                            for param in tool_def.parameters
                        },
                        "required": [
                            param.name for param in tool_def.parameters if param.required
                        ]
                    }
                })
        
        return {"tools": tools}
    
    async def _handle_tools_call(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Handle tool call request."""
        try:
            tool_name = params.get("name")
            arguments = params.get("arguments", {})
            
            if tool_name not in self.all_tools:
                return {
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unknown tool: {tool_name}"
                        }
                    ],
                    "isError": True
                }
            
            # Create tool call
            tool_call = ToolCall(
                tool=tool_name,
                parameters=arguments
            )
            
            # Execute tool
            tool_handler = self.all_tools[tool_name]
            result = await tool_handler.execute_tool(tool_call)
            
            if result.success:
                # Format successful result
                content = []
                
                if isinstance(result.result, dict):
                    # Handle structured results
                    if "data" in result.result and result.result.get("success"):
                        # Screen capture or similar with base64 data
                        content.append({
                            "type": "text",
                            "text": f"Successfully executed {tool_name}"
                        })
                        
                        if result.result["data"]:
                            content.append({
                                "type": "image",
                                "data": result.result["data"],
                                "mimeType": "image/jpeg"
                            })
                    else:
                        # Other structured data
                        content.append({
                            "type": "text",
                            "text": json.dumps(result.result, indent=2)
                        })
                else:
                    # Simple result
                    content.append({
                        "type": "text",
                        "text": str(result.result)
                    })
                
                return {
                    "content": content,
                    "isError": False
                }
            else:
                return {
                    "content": [
                        {
                            "type": "text",
                            "text": f"Tool execution failed: {result.error}"
                        }
                    ],
                    "isError": True
                }
                
        except Exception as e:
            self.logger.error("Tool call error", error=str(e))
            return {
                "content": [
                    {
                        "type": "text",
                        "text": f"Tool call failed: {e}"
                    }
                ],
                "isError": True
            }
    
    async def _handle_ping(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Handle ping request."""
        return {
            "status": "ok",
            "timestamp": asyncio.get_event_loop().time(),
            "server": "matrix-vision-mcp",
            "version": "0.1.0"
        }
    
    async def cleanup(self) -> None:
        """Cleanup server resources."""
        # Cleanup tool handlers
        if hasattr(self.screen_tools, 'cleanup'):
            await self.screen_tools.cleanup()
        if hasattr(self.streaming_tools, 'cleanup'):
            await self.streaming_tools.cleanup()
        if hasattr(self.system_tools, 'cleanup'):
            await self.system_tools.cleanup()
        
        self.logger.info("MCP Server cleaned up")


async def main():
    """Main entry point for MCP server."""
    server = MatrixVisionMCPServer()
    try:
        await server.start()
    finally:
        await server.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
