"""Tests for screen capture functionality."""

import asyncio
import pytest
import numpy as np
from unittest.mock import Mock, patch, AsyncMock

from matrix_vision.capture.screen_capture import <PERSON><PERSON><PERSON><PERSON>, CaptureManager
from matrix_vision.capture.capture_backends import MSSBackend, PILBackend
from matrix_vision.core.exceptions import ScreenCaptureError


class TestScreenCapture:
    """Test screen capture functionality."""
    
    @pytest.fixture
    async def screen_capture(self):
        """Create screen capture instance."""
        capture = ScreenCapture()
        yield capture
        await capture.cleanup()
    
    @pytest.mark.asyncio
    async def test_initialization(self, screen_capture):
        """Test screen capture initialization."""
        assert not screen_capture.is_initialized
        
        await screen_capture.initialize()
        assert screen_capture.is_initialized
    
    @pytest.mark.asyncio
    async def test_capture_frame(self, screen_capture):
        """Test frame capture."""
        # Mock the backend
        mock_frame = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        with patch.object(screen_capture.backend, 'capture_screen', new_callable=AsyncMock) as mock_capture:
            mock_capture.return_value = mock_frame
            
            frame, metadata = await screen_capture.capture_frame()
            
            assert isinstance(frame, np.ndarray)
            assert frame.shape == (100, 100, 3)
            assert 'capture_time' in metadata
            assert 'backend' in metadata
    
    @pytest.mark.asyncio
    async def test_capture_frame_base64(self, screen_capture):
        """Test base64 frame capture."""
        mock_frame = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        with patch.object(screen_capture.backend, 'capture_screen', new_callable=AsyncMock) as mock_capture:
            mock_capture.return_value = mock_frame
            
            base64_data, metadata = await screen_capture.capture_frame_base64()
            
            assert isinstance(base64_data, str)
            assert len(base64_data) > 0
            assert 'encoding' in metadata
            assert metadata['encoding'] == 'base64'
    
    @pytest.mark.asyncio
    async def test_get_monitors(self, screen_capture):
        """Test monitor listing."""
        mock_monitors = [
            {'left': 0, 'top': 0, 'width': 1920, 'height': 1080},
            {'left': 1920, 'top': 0, 'width': 1920, 'height': 1080}
        ]
        
        with patch.object(screen_capture.backend, 'get_monitors', new_callable=AsyncMock) as mock_monitors_call:
            mock_monitors_call.return_value = mock_monitors
            
            monitors = await screen_capture.get_monitors()
            
            assert len(monitors) == 2
            assert monitors[0]['width'] == 1920


class TestCaptureBackends:
    """Test capture backends."""
    
    @pytest.mark.asyncio
    async def test_mss_backend_initialization(self):
        """Test MSS backend initialization."""
        backend = MSSBackend()
        
        with patch('mss.mss') as mock_mss:
            mock_instance = Mock()
            mock_instance.monitors = [
                {'left': 0, 'top': 0, 'width': 1920, 'height': 1080}
            ]
            mock_mss.return_value = mock_instance
            
            await backend.initialize()
            
            assert backend.is_initialized
            assert len(backend._monitors) == 1
    
    @pytest.mark.asyncio
    async def test_pil_backend_initialization(self):
        """Test PIL backend initialization."""
        backend = PILBackend()
        
        with patch('PIL.ImageGrab.grab') as mock_grab:
            mock_image = Mock()
            mock_image.size = (1920, 1080)
            mock_grab.return_value = mock_image
            
            await backend.initialize()
            
            assert backend.is_initialized
            assert backend._screen_size == (1920, 1080)


class TestCaptureManager:
    """Test capture manager."""
    
    @pytest.fixture
    async def capture_manager(self):
        """Create capture manager instance."""
        manager = CaptureManager()
        yield manager
        await manager.cleanup()
    
    @pytest.mark.asyncio
    async def test_start_stop_capture(self, capture_manager):
        """Test starting and stopping capture."""
        assert not capture_manager.is_running
        
        # Mock the capture system
        with patch.object(capture_manager.capture, 'initialize', new_callable=AsyncMock):
            with patch.object(capture_manager.capture, 'capture_frame', new_callable=AsyncMock) as mock_capture:
                mock_frame = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
                mock_metadata = {'capture_time': 123456.789}
                mock_capture.return_value = (mock_frame, mock_metadata)
                
                await capture_manager.start_capture()
                assert capture_manager.is_running
                
                # Let it capture a few frames
                await asyncio.sleep(0.1)
                
                await capture_manager.stop_capture()
                assert not capture_manager.is_running
    
    @pytest.mark.asyncio
    async def test_get_latest_frame(self, capture_manager):
        """Test getting latest frame."""
        mock_frame = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        # Add frame to buffer
        await capture_manager.frame_buffer.put_frame(mock_frame, 123456.789)
        
        frame_data = await capture_manager.get_latest_frame()
        
        assert frame_data is not None
        assert np.array_equal(frame_data['frame'], mock_frame)
        assert frame_data['timestamp'] == 123456.789
    
    def test_stats(self, capture_manager):
        """Test statistics."""
        stats = capture_manager.stats
        
        assert 'frames_captured' in stats
        assert 'frames_dropped' in stats
        assert 'runtime_seconds' in stats
        assert 'is_running' in stats


class TestErrorHandling:
    """Test error handling."""
    
    @pytest.mark.asyncio
    async def test_backend_initialization_error(self):
        """Test backend initialization error."""
        backend = MSSBackend()
        
        with patch('mss.mss', side_effect=ImportError("MSS not available")):
            with pytest.raises(ScreenCaptureError):
                await backend.initialize()
    
    @pytest.mark.asyncio
    async def test_capture_error(self):
        """Test capture error handling."""
        capture = ScreenCapture()
        
        with patch.object(capture.backend, 'capture_screen', side_effect=Exception("Capture failed")):
            with pytest.raises(ScreenCaptureError):
                await capture.capture_frame()


@pytest.mark.integration
class TestIntegration:
    """Integration tests (require actual screen)."""
    
    @pytest.mark.asyncio
    async def test_real_screen_capture(self):
        """Test real screen capture (if available)."""
        try:
            capture = ScreenCapture()
            await capture.initialize()
            
            frame, metadata = await capture.capture_frame()
            
            assert isinstance(frame, np.ndarray)
            assert len(frame.shape) == 3  # Height, Width, Channels
            assert frame.shape[2] == 3  # RGB
            assert frame.dtype == np.uint8
            
            await capture.cleanup()
            
        except Exception as e:
            pytest.skip(f"Real screen capture not available: {e}")
    
    @pytest.mark.asyncio
    async def test_real_monitors_list(self):
        """Test real monitors listing (if available)."""
        try:
            capture = ScreenCapture()
            await capture.initialize()
            
            monitors = await capture.get_monitors()
            
            assert isinstance(monitors, list)
            assert len(monitors) > 0
            
            for monitor in monitors:
                assert 'width' in monitor
                assert 'height' in monitor
                assert monitor['width'] > 0
                assert monitor['height'] > 0
            
            await capture.cleanup()
            
        except Exception as e:
            pytest.skip(f"Real monitors listing not available: {e}")


if __name__ == "__main__":
    pytest.main([__file__])
