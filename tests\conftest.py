"""Pytest configuration and fixtures."""

import asyncio
import pytest
import sys
from pathlib import Path

# Add src to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_screen_data():
    """Mock screen data for testing."""
    import numpy as np
    return np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)


@pytest.fixture
def mock_base64_data():
    """Mock base64 encoded data."""
    return "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="


@pytest.fixture
def mock_metadata():
    """Mock metadata for testing."""
    return {
        "timestamp": 1234567890.123,
        "width": 100,
        "height": 100,
        "format": "RGB",
        "capture_time": 1234567890.123,
        "backend": "mock"
    }


@pytest.fixture
def temp_output_dir(tmp_path):
    """Create temporary output directory."""
    output_dir = tmp_path / "output"
    output_dir.mkdir()
    return output_dir


# Pytest markers
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "gpu: marks tests that require GPU"
    )


# Skip tests that require actual screen capture in CI
def pytest_collection_modifyitems(config, items):
    """Modify test collection to handle CI environment."""
    import os
    
    if os.environ.get("CI") or os.environ.get("GITHUB_ACTIONS"):
        skip_integration = pytest.mark.skip(reason="Skipping integration tests in CI")
        for item in items:
            if "integration" in item.keywords:
                item.add_marker(skip_integration)
