"""Frame buffer and processing utilities for efficient screen capture."""

import asyncio
import time
from collections import deque
from typing import Callable, Optional, Tuple

import numpy as np

from ..core.config import settings
from ..core.exceptions import PerformanceError
from ..core.logger import LoggerMixin, PerformanceLogger


class FrameBuffer(LoggerMixin):
    """Thread-safe frame buffer for managing captured frames."""
    
    def __init__(self, max_size: int = 10):
        self.max_size = max_size
        self._buffer = deque(maxlen=max_size)
        self._lock = asyncio.Lock()
        self._frame_count = 0
        self._dropped_frames = 0
        
    async def put_frame(self, frame: np.ndarray, timestamp: float = None) -> bool:
        """
        Add a frame to the buffer.
        
        Args:
            frame: Frame data as numpy array
            timestamp: Frame timestamp (defaults to current time)
            
        Returns:
            True if frame was added, False if dropped
        """
        if timestamp is None:
            timestamp = time.perf_counter()
        
        async with self._lock:
            if len(self._buffer) >= self.max_size:
                # Buffer is full, drop oldest frame
                self._buffer.popleft()
                self._dropped_frames += 1
                self.logger.debug("Frame dropped due to full buffer")
            
            self._buffer.append({
                "frame": frame,
                "timestamp": timestamp,
                "frame_id": self._frame_count
            })
            self._frame_count += 1
            
        return True
    
    async def get_frame(self, timeout: float = 1.0) -> Optional[dict]:
        """
        Get the latest frame from buffer.
        
        Args:
            timeout: Maximum time to wait for a frame
            
        Returns:
            Frame data dict or None if timeout
        """
        start_time = time.perf_counter()
        
        while time.perf_counter() - start_time < timeout:
            async with self._lock:
                if self._buffer:
                    return self._buffer.popleft()
            
            # Wait a bit before checking again
            await asyncio.sleep(0.001)
        
        return None
    
    async def get_latest_frame(self) -> Optional[dict]:
        """Get the most recent frame, discarding older ones."""
        async with self._lock:
            if not self._buffer:
                return None
            
            # Get the latest frame and clear buffer
            latest_frame = self._buffer[-1]
            self._buffer.clear()
            return latest_frame
    
    async def clear(self) -> None:
        """Clear all frames from buffer."""
        async with self._lock:
            self._buffer.clear()
    
    @property
    def size(self) -> int:
        """Get current buffer size."""
        return len(self._buffer)
    
    @property
    def is_empty(self) -> bool:
        """Check if buffer is empty."""
        return len(self._buffer) == 0
    
    @property
    def is_full(self) -> bool:
        """Check if buffer is full."""
        return len(self._buffer) >= self.max_size
    
    @property
    def stats(self) -> dict:
        """Get buffer statistics."""
        return {
            "size": len(self._buffer),
            "max_size": self.max_size,
            "frame_count": self._frame_count,
            "dropped_frames": self._dropped_frames,
            "drop_rate": self._dropped_frames / max(1, self._frame_count)
        }


class FrameProcessor(LoggerMixin):
    """Processes frames with rate limiting and quality control."""

    def __init__(
        self,
        target_fps: int = 30,
        quality: int = 85,
        max_width: int = 1920,
        max_height: int = 1080
    ):
        self.target_fps = target_fps
        self.quality = quality
        self.max_width = max_width
        self.max_height = max_height

        # Rate limiting
        self._last_process_time = 0.0
        self._frame_interval = 1.0 / target_fps

        # Performance monitoring
        self._perf_logger = PerformanceLogger(self.logger)
        self._process_times = deque(maxlen=100)
        
    async def process_frame(
        self,
        frame: np.ndarray,
        resize: bool = True,
        format_output: str = "RGB"
    ) -> Tuple[np.ndarray, dict]:
        """
        Process a frame with resizing and format conversion.

        Args:
            frame: Input frame as numpy array
            resize: Whether to resize frame to max dimensions
            format_output: Output format ("RGB", "BGR", "GRAY")

        Returns:
            Tuple of (processed_frame, metadata)
        """
        # Simple rate limiting
        current_time = time.perf_counter()
        time_since_last = current_time - self._last_process_time
        if time_since_last < self._frame_interval:
            await asyncio.sleep(self._frame_interval - time_since_last)
        self._last_process_time = time.perf_counter()
            self._perf_logger.start_timer("frame_process")
            
            try:
                # Get original dimensions
                original_height, original_width = frame.shape[:2]
                
                # Resize if needed
                if resize and (original_width > self.max_width or original_height > self.max_height):
                    frame = await self._resize_frame(frame, self.max_width, self.max_height)
                
                # Format conversion
                if format_output == "BGR" and frame.shape[2] == 3:
                    frame = frame[:, :, ::-1]  # RGB to BGR
                elif format_output == "GRAY":
                    if len(frame.shape) == 3:
                        # Convert to grayscale
                        frame = np.dot(frame[..., :3], [0.2989, 0.5870, 0.1140])
                        frame = frame.astype(np.uint8)
                
                # Create metadata
                metadata = {
                    "original_size": (original_width, original_height),
                    "processed_size": frame.shape[1::-1] if len(frame.shape) == 3 else frame.shape[::-1],
                    "format": format_output,
                    "quality": self.quality,
                    "timestamp": time.perf_counter()
                }
                
                # Record processing time
                duration = self._perf_logger.end_timer("frame_process")
                self._process_times.append(duration)
                
                # Check performance
                if len(self._process_times) >= 10:
                    avg_time = sum(self._process_times) / len(self._process_times)
                    max_time = 1.0 / self.target_fps
                    
                    if avg_time > max_time * 1.5:  # 50% over target
                        self.logger.warning(
                            "Frame processing too slow",
                            avg_time_ms=round(avg_time * 1000, 2),
                            target_time_ms=round(max_time * 1000, 2),
                            fps_impact=round(1.0 / avg_time, 1)
                        )
                
                return frame, metadata
                
            except Exception as e:
                self._perf_logger.end_timer("frame_process")
                raise PerformanceError(f"Frame processing failed: {e}") from e
    
    async def _resize_frame(
        self,
        frame: np.ndarray,
        max_width: int,
        max_height: int
    ) -> np.ndarray:
        """Resize frame maintaining aspect ratio."""
        height, width = frame.shape[:2]
        
        # Calculate scaling factor
        scale_w = max_width / width
        scale_h = max_height / height
        scale = min(scale_w, scale_h)
        
        if scale >= 1.0:
            return frame  # No need to resize
        
        # Calculate new dimensions
        new_width = int(width * scale)
        new_height = int(height * scale)
        
        # Use OpenCV for high-quality resizing if available
        try:
            import cv2
            loop = asyncio.get_event_loop()
            resized = await loop.run_in_executor(
                None,
                cv2.resize,
                frame,
                (new_width, new_height),
                cv2.INTER_LANCZOS4
            )
            return resized
        except ImportError:
            # Fallback to PIL
            from PIL import Image
            
            loop = asyncio.get_event_loop()
            
            def _pil_resize():
                img = Image.fromarray(frame)
                img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                return np.array(img)
            
            return await loop.run_in_executor(None, _pil_resize)
    
    async def set_target_fps(self, fps: int) -> None:
        """Update target FPS and frame interval."""
        self.target_fps = fps
        self._frame_interval = 1.0 / fps
        self.logger.info("Target FPS updated", fps=fps)
    
    @property
    def performance_stats(self) -> dict:
        """Get performance statistics."""
        if not self._process_times:
            return {"avg_time_ms": 0, "max_time_ms": 0, "min_time_ms": 0}
        
        times_ms = [t * 1000 for t in self._process_times]
        return {
            "avg_time_ms": round(sum(times_ms) / len(times_ms), 2),
            "max_time_ms": round(max(times_ms), 2),
            "min_time_ms": round(min(times_ms), 2),
            "sample_count": len(times_ms),
            "target_fps": self.target_fps
        }
