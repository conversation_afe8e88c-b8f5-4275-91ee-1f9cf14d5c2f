Bir "Matrix Vision" projesi geliştirmek istiyorum. Bu proje, yapay zekanın dünyayı nasıl "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>" simüle eden bir sistem olacak. Matrix filmindeki yeşil akan kodlar gibi, kullanı<PERSON>ının ekranını gerçek zamanlı olarak yakalayıp, özel efektlerle dönüştüren ve stream eden bir uygulama geliştirmemi istiyorum.

Proje gereksinimleri:

Sürekli açık kalan bir server
WebSocket üzerinden ekran görüntüsü streaming
Base64 formatında görüntü aktarımı
MCP (Model Context Protocol) tool entegrasyonu
Matrix film efekti (yeşil akan kodlar)
Asenkron mimari
Kullanıcı tarafından başlatılıp durdurulabilme
Farklı efekt seçenekleri
Teknik detaylar:

Python kullanarak geliştirme
WebSocket protokolü ile iletişim
Ekran görüntüsü yakalama için PyAutoGUI
Görüntü işleme için PIL/Pillow
Asenkron işlemler için asyncio
MCP tool entegrasyonu için özel bir arayüz
Lütfen bu projeyi adım adım geliştirmeme yardımcı ol. Öncelikle mimariyi tasarla, sonra her bir bileşeni detaylı olarak kodla ve son olarak bunları nasıl entegre edeceğimi açıkla.

Bu prompt ile Matrix Vision projesini geliştirmeye başlayalım. Öncelikle projenin genel mimarisini tasarlayarak başlayalım.

1. Proje Mimarisi
Matrix Vision projesi için şu ana bileşenleri tasarlayacağız:

Server Bileşeni: Sürekli çalışan, WebSocket bağlantılarını yöneten ana sunucu
Ekran Yakalama Modülü: Ekran görüntülerini yakalayan ve işleyen modül
Efekt İşleme Motoru: Matrix benzeri efektleri uygulayan motor
Client Bileşeni: Görüntüleri alan ve gösteren istemci
MCP Tool Entegrasyonu: Yapay zeka modellerinin sistemi kontrol etmesini sağlayan arabirim

2. Klasör Yapısı

matrix_vision/
├── server/
│   ├── __init__.py
│   ├── server.py
│   └── screen_capture.py
├── client/
│   ├── __init__.py
│   ├── client.py
│   └── display.py
├── effects/
│   ├── __init__.py
│   ├── matrix_effect.py
│   └── effect_factory.py
├── integration/
│   ├── __init__.py
│   └── mcp_tool.py
├── utils/
│   ├── __init__.py
│   └── config.py
├── main.py
└── requirements.txt

3. Bağımlılıklar
websockets==10.4
pyautogui==0.9.53
pillow==9.4.0
numpy==1.24.2

Şimdi her bir bileşeni detaylı olarak kodlayalım:

4. Server Bileşeni

import asyncio
import json
import websockets
import logging
from .screen_capture import ScreenCapture

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MatrixVisionServer:
    def __init__(self, host="localhost", port=8765):
        self.host = host
        self.port = port
        self.active_clients = {}
        self.running = False
        self.screen_capture = ScreenCapture()
        
    async def start(self):
        self.running = True
        logger.info(f"Matrix Vision Server başlatılıyor: ws://{self.host}:{self.port}")
        
        async with websockets.serve(self.handle_client, self.host, self.port):
            await asyncio.Future()  # Sonsuza kadar çalış
            
    async def handle_client(self, websocket, path):
        client_id = id(websocket)
        self.active_clients[client_id] = {
            "websocket": websocket,
            "streaming": False,
            "fps": 10,
            "effect": "matrix_classic"
        }
        
        logger.info(f"Yeni client bağlandı: {client_id}")
        
        try:
            async for message in websocket:
                await self.process_message(client_id, message)
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Client bağlantısı kapandı: {client_id}")
        finally:
            if client_id in self.active_clients:
                if self.active_clients[client_id]["streaming"]:
                    # Streaming durdur
                    self.active_clients[client_id]["streaming"] = False
                del self.active_clients[client_id]
    
    async def process_message(self, client_id, message):
        try:
            data = json.loads(message)
            action = data.get("action", "")
            
            if action == "start_stream":
                await self.start_streaming(client_id, data)
            elif action == "stop_stream":
                await self.stop_streaming(client_id)
            elif action == "change_effect":
                await self.change_effect(client_id, data)
            else:
                await self.send_error(client_id, f"Bilinmeyen eylem: {action}")
                
        except json.JSONDecodeError:
            await self.send_error(client_id, "Geçersiz JSON formatı")
        except Exception as e:
            await self.send_error(client_id, f"İşlem hatası: {str(e)}")
    
    async def start_streaming(self, client_id, data):
        if client_id not in self.active_clients:
            return
            
        client = self.active_clients[client_id]
        client["fps"] = data.get("fps", 10)
        client["effect"] = data.get("effect", "matrix_classic")
        client["streaming"] = True
        
        logger.info(f"Client {client_id} için streaming başlatılıyor, FPS: {client['fps']}")
        
        # Streaming task başlat
        asyncio.create_task(self.stream_to_client(client_id))
        
        await self.send_response(client_id, {
            "status": "success",
            "message": "Streaming başlatıldı"
        })
    
    async def stop_streaming(self, client_id):
        if client_id in self.active_clients:
            self.active_clients[client_id]["streaming"] = False
            logger.info(f"Client {client_id} için streaming durduruldu")
            
            await self.send_response(client_id, {
                "status": "success",
                "message": "Streaming durduruldu"
            })
    
    async def change_effect(self, client_id, data):
        if client_id in self.active_clients:
            effect = data.get("effect", "matrix_classic")
            self.active_clients[client_id]["effect"] = effect
            logger.info(f"Client {client_id} için efekt değiştirildi: {effect}")
            
            await self.send_response(client_id, {
                "status": "success",
                "message": f"Efekt değiştirildi: {effect}"
            })
    
    async def stream_to_client(self, client_id):
        if client_id not in self.active_clients:
            return
            
        client = self.active_clients[client_id]
        interval = 1.0 / client["fps"]
        
        while client_id in self.active_clients and self.active_clients[client_id]["streaming"]:
            try:
                # Ekran görüntüsü al
                screen_data = await self.screen_capture.capture_screen_base64(
                    effect=client["effect"]
                )
                
                # Görüntüyü gönder
                await client["websocket"].send(json.dumps({
                    "type": "screen_data",
                    "timestamp": self.screen_capture.get_timestamp(),
                    "effect": client["effect"],
                    "data": screen_data
                }))
                
                await asyncio.sleep(interval)
            except Exception as e:
                logger.error(f"Streaming hatası: {str(e)}")
                break
    
    async def send_response(self, client_id, data):
        if client_id in self.active_clients:
            await self.active_clients[client_id]["websocket"].send(json.dumps(data))
    
    async def send_error(self, client_id, error_message):
        await self.send_response(client_id, {
            "status": "error",
            "message": error_message
        })






server/screen_capture.py
        
        import asyncio
import base64
import io
import time
import pyautogui
from PIL import Image
import logging

logger = logging.getLogger(__name__)

class ScreenCapture:
    def __init__(self):
        self.last_capture_time = 0
        
    def get_timestamp(self):
        return time.time()
    
    async def capture_screen_base64(self, effect=None):
        """Ekran görüntüsünü yakalar, efekt uygular ve base64 formatına dönüştürür"""
        try:
            # Ekran görüntüsünü yakala (CPU-bound işlem)
            loop = asyncio.get_event_loop()
            screenshot = await loop.run_in_executor(None, pyautogui.screenshot)
            
            # Efekt uygula (eğer belirtilmişse)
            if effect:
                # Efekt uygulamak için effects modülünü kullanacağız
                # Şimdilik basit bir dönüşüm yapalım
                screenshot = await self._apply_simple_effect(screenshot, effect)
            
            # Base64'e dönüştür
            buffered = io.BytesIO()
            screenshot.save(buffered, format="JPEG", quality=70)
            img_str = base64.b64encode(buffered.getvalue()).decode()
            
            self.last_capture_time = self.get_timestamp()
            return img_str
            
        except Exception as e:
            logger.error(f"Ekran yakalama hatası: {str(e)}")
            raise
    
    async def _apply_simple_effect(self, image, effect_name):
        """Basit bir efekt uygular (gerçek uygulamada effects modülü kullanılacak)"""
        # Bu fonksiyon, gerçek effects modülü tamamlanana kadar geçici olarak kullanılacak
        if effect_name == "matrix_classic":
            # Yeşil renk tonu uygula
            from PIL import ImageEnhance, ImageOps
            
            # Görüntüyü gri tonlamalı yap
            image = ImageOps.grayscale(image)
            
            # Kontrastı artır
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.5)
            
            # Yeşil renk tonu uygula
            from PIL import ImageOps, ImageChops
            
            # Yeşil kanal oluştur
            green = Image.new('RGB', image.size, (0, 255, 0))
            
            # Gri tonlamalı görüntüyü maske olarak kullan
            result = ImageChops.multiply(green, image.convert('RGB'))
            
            return result
        
        # Efekt tanınmıyorsa orijinal görüntüyü döndür
        return image

        
       5. Efekt İşleme Motoru

       import numpy as np
from PIL import Image, ImageDraw, ImageFont, ImageEnhance, ImageOps
import random
import asyncio
import os

class MatrixEffect:
    def __init__(self):
        # Matrix karakterleri (Japon Katakana, sayılar ve semboller)
        self.matrix_chars = [chr(i) for i in range(0x30A1, 0x30FF)]
        self.matrix_chars.extend([str(i) for i in range(10)])
        self.matrix_chars.extend(['$', '%', '#', '*', '+', '-', ':', ';', '=', '?', '@'])
        
        # Font yükleme
        font_path = os.path.join(os.path.dirname(__file__), '../assets/fonts/NotoSansJP-Regular.ttf')
        try:
            self.font = ImageFont.truetype(font_path, 14)
        except IOError:
            # Font bulunamazsa varsayılan font kullan
            self.font = ImageFont.load_default()
        
        # Efekt parametreleri
        self.rain_density = 0.05  # Yağmur damlası yoğunluğu
        self.trail_length = 25    # İz uzunluğu
        self.glow_intensity = 1.5 # Parlaklık yoğunluğu
        
        # Raindrop pozisyonları
        self.raindrops = {}
    
    async def apply(self, image):
        """Matrix efektini görüntüye uygular"""
        # CPU-bound işlem olduğu için executor'da çalıştır
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self._process_image, image)
    
    def _process_image(self, image):
        """Matrix efektini görüntüye uygular (senkron işlem)"""
        # Görüntüyü gri tonlamalı yap ve kontrastı artır
        gray_image = ImageOps.grayscale(image)
        enhancer = ImageEnhance.Contrast(gray_image)
        high_contrast = enhancer.enhance(1.8)
        
        # Kenarları algıla (basit bir yaklaşım)
        edges = self._detect_edges(high_contrast)
        
        # Yeni bir görüntü oluştur (siyah arka plan)
        width, height = image.size
        matrix_image = Image.new('RGB', (width, height), (0, 0, 0))
        draw = ImageDraw.Draw(matrix_image)
        
        # Raindrop'ları güncelle
        self._update_raindrops(width, height, edges)
        
        # Raindrop'ları çiz
        for x, drops in self.raindrops.items():
            for y, char_info in drops.items():
                char, brightness = char_info
                # Parlaklığa göre yeşil renk tonu
                green = int(min(255, 50 + brightness * 205))
                draw.text((x, y), char, fill=(0, green, 0), font=self.font)
        
        return matrix_image
    
    def _detect_edges(self, image):
        """Basit kenar algılama"""
        # NumPy array'e dönüştür
        img_array = np.array(image)
        
        # Basit bir gradyan hesapla
        gradient_x = np.abs(np.gradient(img_array, axis=1))
        gradient_y = np.abs(np.gradient(img_array, axis=0))
        
        # Gradyanları birleştir
        edges = np.sqrt(gradient_x**2 + gradient_y**2)
        
        # Normalize et
        edges = edges / edges.max() if edges.max() > 0 else edges
        
        return edges
    
    def _update_raindrops(self, width, height, edges):
        """Raindrop pozisyonlarını güncelle"""
        # Yeni raindrop'lar ekle
        for x in range(0, width, 15):  # 15 piksel aralıklarla
            # Rastgele olarak yeni raindrop'lar oluştur
            if random.random() < self.rain_density:
                if x not in self.raindrops:
                    self.raindrops[x] = {}
                
                # En üstte başlat
                y = 0
                char = random.choice(self.matrix_chars)
                self.raindrops[x][y] = (char, 1.0)  # (karakter, parlaklık)
        
        # Mevcut raindrop'ları güncelle
        new_raindrops = {}
        for x, drops in self.raindrops.items():
            new_drops = {}
            
            for y, (char, brightness) in drops.items():
                # Raindrop'u aşağı taşı
                new_y = y + 15
                
                # Eğer ekranın dışına çıktıysa, bu raindrop'u kaldır
                if new_y >= height:
                    continue
                
                # Kenar algılama sonucuna göre parlaklığı ayarla
                edge_value = edges[min(new_y, height-1), min(x, width-1)]
                new_brightness = min(1.0, brightness * 0.95 + edge_value * 0.5)
                
                # Rastgele olarak karakteri değiştir
                if random.random() < 0.1:
                    char = random.choice(self.matrix_chars)
                
                new_drops[new_y] = (char, new_brightness)
                
                # İz efekti için soluklaşan karakterler ekle
                for i in range(1, self.trail_length):
                    trail_y = y - i * 15
                    if trail_y >= 0 and trail_y not in new_drops:
                        trail_brightness = brightness * (1 - i / self.trail_length)
                        if trail_brightness > 0.05:  # Çok soluk olanları gösterme
                            new_drops[trail_y] = (random.choice(self.matrix_chars), trail_brightness)
            
            if new_drops:
                new_raindrops[x] = new_drops
        
        self.raindrops = new_raindrops

        
effects/effect_factory.py


from .matrix_effect import MatrixEffect

class EffectFactory:
    """Farklı efektleri oluşturmak için fabrika sınıfı"""
    
    _effects = {}
    
    @classmethod
    def get_effect(cls, effect_name):
        """Belirtilen efekti döndürür, yoksa oluşturur"""
        if effect_name not in cls._effects:
            if effect_name == "matrix_classic":
                cls._effects[effect_name] = MatrixEffect()
            elif effect_name == "matrix_dense":
                effect = MatrixEffect()
                effect.rain_density = 0.1
                effect.trail_length = 35
                cls._effects[effect_name] = effect
            elif effect_name == "matrix_sparse":
                effect = MatrixEffect()
                effect.rain_density = 0.02
                effect.trail_length = 15
                cls._effects[effect_name] = effect
            else:
                # Varsayılan olarak klasik matrix efekti
                cls._effects[effect_name] = MatrixEffect()
        
        return cls._effects[effect_name]
    
    @classmethod
    async def apply_effect(cls, image, effect_name):
        """Belirtilen efekti görüntüye uygular"""
        effect = cls.get_effect(effect_name)
        return await effect.apply(image)


        6. Client Bileşeni
client/client.py

import asyncio
import json
import websockets
import base64
import logging
from io import BytesIO
from PIL import Image

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MatrixVisionClient:
    def __init__(self, server_url="ws://localhost:8765"):
        self.server_url = server_url
        self.websocket = None
        self.connected = False
        self.streaming = False
        self.display_callback = None
        
    async def connect(self):
        """Sunucuya bağlan"""
        try:
            self.websocket = await websockets.connect(self.server_url)
            self.connected = True
            logger.info(f"Sunucuya bağlandı: {self.server_url}")
            
            # Mesaj dinleme döngüsünü başlat
            asyncio.create_task(self._listen_messages())
            
            return True
        except Exception as e:
            logger.error(f"Bağlantı hatası: {str(e)}")
            self.connected = False
            return False
    
    async def disconnect(self):
        """Sunucudan bağlantıyı kes"""
        if self.connected and self.websocket:
            await self.websocket.close()
            self.connected = False
            self.streaming = False
            logger.info("Sunucudan bağlantı kesildi")
    
    async def start_stream(self, fps=10, effect="matrix_classic"):
        """Görüntü akışını başlat"""
        if not self.connected:
            success = await self.connect()
            if not success:
                return False
        
        try:
            await self.websocket.send(json.dumps({
                "action": "start_stream",
                "fps": fps,
                "effect": effect
            }))
            
            self.streaming = True
            logger.info(f"Streaming başlatıldı, FPS: {fps}, Efekt: {effect}")
            return True
        except Exception as e:
            logger.error(f"Streaming başlatma hatası: {str(e)}")
            return False
    
    async def stop_stream(self):
        """Görüntü akışını durdur"""
        if not self.connected or not self.streaming:
            return False
        
        try:
            await self.websocket.send(json.dumps({
                "action": "stop_stream"
            }))
            
            self.streaming = False
            logger.info("Streaming durduruldu")
            return True
        except Exception as e:
            logger.error(f"Streaming durdurma hatası: {str(e)}")
            return False
    
    async def change_effect(self, effect):
        """Efekti değiştir"""
        if not self.connected:
            return False
        
        try:
            await self.websocket.send(json.dumps({
                "action": "change_effect",
                "effect": effect
            }))
            
            logger.info(f"Efekt değiştirildi: {effect}")
            return True
        except Exception as e:
            logger.error(f"Efekt değiştirme hatası: {str(e)}")
            return False
    
    def set_display_callback(self, callback):
        """Görüntü gösterme callback'ini ayarla"""
        self.display_callback = callback
    
    async def _listen_messages(self):
        """Sunucudan gelen mesajları dinle"""
        if not self.websocket:
            return
        
        try:
            async for message in self.websocket:
                await self._process_message(message)
        except websockets.exceptions.ConnectionClosed:
            logger.info("Sunucu bağlantısı kapandı")
            self.connected = False
            self.streaming = False
        except Exception as e:
            logger.error(f"Mesaj dinleme hatası: {str(e)}")
    
    async def _process_message(self, message):
        """Gelen mesajı işle"""
        try:
            data = json.loads(message)
            
            if data.get("type") == "screen_data":
                # Ekran verisi geldi, görüntüyü işle
                await self._process_screen_data(data)
            elif data.get("status") == "error":
                # Hata mesajı
                logger.error(f"Sunucu hatası: {data.get('message')}")
            else:
                # Diğer mesajlar
                logger.info(f"Sunucu mesajı: {data}")
                
        except json.JSONDecodeError:
            logger.error("Geçersiz JSON formatı")
        except Exception as e:
            logger.error(f"Mesaj işleme hatası: {str(e)}")
    
    async def _process_screen_data(self, data):
        """Ekran verisini işle"""
        try:
            # Base64 verisini çöz
            img_data = base64.b64decode(data["data"])
            
            # PIL Image'e dönüştür
            image = Image.open(BytesIO(img_data))
            
            # Callback varsa çağır
            if self.display_callback:
                self.display_callback(image, data.get("effect"), data.get("timestamp"))
                
        except Exception as e:
            logger.error(f"Ekran verisi işleme hatası: {str(e)}")


            client/display.py
            import tkinter as tk
from PIL import ImageTk
import logging

logger = logging.getLogger(__name__)

class MatrixDisplay:
    def __init__(self, title="Matrix Vision", width=800, height=600):
        self.width = width
        self.height = height
        
        # Tkinter penceresi oluştur
        self.root = tk.Tk()
        self.root.title(title)
        self.root.geometry(f"{width}x{height}")
        self.root.configure(bg="black")
        
        # Görüntü etiketi
        self.image_label = tk.Label(self.root, bg="black")
        self.image_label.pack(fill=tk.BOTH, expand=True)
        
        # Durum çubuğu
        self.status_bar = tk.Label(self.root, text="Bağlı değil", bd=1, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Pencere kapatma olayını yakala
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)
        
        self.running = False
        self.on_close_callback = None
    
    def set_on_close_callback(self, callback):
        """Pencere kapatma callback'ini ayarla"""
        self.on_close_callback = callback
    
    def start(self):
        """Görüntüleme penceresini başlat"""
        self.running = True
        self.root.mainloop()
    
    def stop(self):
        """Görüntüleme penceresini durdur"""
        if self.running:
            self.running = False
            self.root.quit()
    
    def update_image(self, image, effect=None, timestamp=None):
        """Görüntüyü güncelle"""
        try:
            # Görüntüyü pencere boyutuna ölçekle
            image = image.resize((self.width, self.height))
            
            # PIL Image'i Tkinter PhotoImage'e dönüştür
            tk_image = ImageTk.PhotoImage(image)
            
            # Etiketi güncelle
            self.image_label.configure(image=tk_image)
            self.image_label.image = tk_image  # Referansı tut
            
            # Durum çubuğunu güncelle
            status_text = f"Efekt: {effect}" if effect else "Görüntüleniyor"
            if timestamp:
                status_text += f" | Zaman: {timestamp:.2f}"
            
            self.status_bar.config(text=status_text)
            
        except Exception as e:
            logger.error(f"Görüntü güncelleme hatası: {str(e)}")
    
    def on_close(self):
        """Pencere kapatma olayı"""
        if self.on_close_callback:
            self.on_close_callback()
        
        self.stop()


   MCP Tool Entegrasyonu      
   integration/mcp_tool.py
   import asyncio
import json
import logging
from ..client.client import MatrixVisionClient

logger = logging.getLogger(__name__)

class MatrixVisionTool:
    """MCP Tool entegrasyonu için sınıf"""
    
    def __init__(self, server_url="ws://localhost:8765"):
        self.client = MatrixVisionClient(server_url)
        self.active = False
        self.current_effect = "matrix_classic"
        self.current_fps = 10
    
    async def handle_tool_request(self, request):
        """MCP Tool isteğini işle"""
        try:
            # İstek JSON formatında değilse, string olarak kabul et
            if isinstance(request, str):
                try:
                    request = json.loads(request)
                except json.JSONDecodeError:
                    return {
                        "status": "error",
                        "message": "Geçersiz JSON formatı"
                    }
            
            action = request.get("action", "")
            
            if action == "start_vision":
                return await self._start_vision(request)
            elif action == "stop_vision":
                return await self._stop_vision()
            elif action == "change_effect":
                return await self._change_effect(request)
            elif action == "status":
                return self._get_status()
            else:
                return {
                    "status": "error",
                    "message": f"Bilinmeyen eylem: {action}"
                }
                
        except Exception as e:
            logger.error(f"Tool isteği işleme hatası: {str(e)}")
            return {
                "status": "error",
                "message": f"İşlem hatası: {str(e)}"
            }
    
    async def _start_vision(self, request):
        """Matrix görüşünü başlat"""
        fps = request.get("fps", 10)
        effect = request.get("effect", "matrix_classic")
        
        self.current_fps = fps
        self.current_effect = effect
        
        success = await self.client.start_stream(fps, effect)
        
        if success:
            self.active = True
            return {
                "status": "success",
                "message": f"Matrix görüşü başlatıldı (FPS: {fps}, Efekt: {effect})"
            }
        else:
            return {
                "status": "error",
                "message": "Matrix görüşü baş