"""
Matrix Vision - AI-powered Matrix digital rain effect with real-time screen capture streaming.

This package provides a high-performance system for capturing screen content,
applying Matrix-style digital rain effects, and streaming the results via WebSocket.
"""

__version__ = "0.1.0"
__author__ = "inkbytefo"
__email__ = "<EMAIL>"

from .core.config import Settings
from .core.logger import get_logger

# Package-level logger
logger = get_logger(__name__)

# Version info
VERSION_INFO = tuple(map(int, __version__.split(".")))

# Public API
__all__ = [
    "__version__",
    "__author__",
    "__email__",
    "VERSION_INFO",
    "Settings",
    "get_logger",
]
