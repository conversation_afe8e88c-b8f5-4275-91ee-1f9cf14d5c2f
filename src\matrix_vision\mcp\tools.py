"""MCP tools implementation for Matrix Vision."""

import asyncio
import time
from typing import Any, Dict, List, Optional

from ..capture.screen_capture import <PERSON><PERSON><PERSON><PERSON>, CaptureManager
from ..streaming.screen_streamer import ScreenStreamer
from ..streaming.ai_interface import AIInterface, StreamingSession
from ..streaming.data_encoder import DataEncoder, EncodingFormat
from ..core.config import settings
from ..core.exceptions import ScreenCaptureError, MCPError
from ..core.logger import LoggerMixin
from .schemas import (
    ToolDefinition, ToolParameter, ToolCall, ToolResult,
    ScreenCaptureParams, StreamingParams, SystemInfoParams,
    ScreenCaptureResult, StreamingResult, SystemInfoResult
)


class BaseMCPTools(LoggerMixin):
    """Base class for MCP tools."""
    
    def __init__(self):
        self.tools: Dict[str, ToolDefinition] = {}
        self._register_tools()
    
    def _register_tools(self) -> None:
        """Register available tools. Override in subclasses."""
        pass
    
    def get_tools(self) -> List[ToolDefinition]:
        """Get list of available tools."""
        return list(self.tools.values())
    
    def get_tool(self, name: str) -> Optional[ToolDefinition]:
        """Get tool definition by name."""
        return self.tools.get(name)
    
    async def execute_tool(self, call: ToolCall) -> ToolResult:
        """Execute a tool call."""
        tool_name = call.tool
        
        if tool_name not in self.tools:
            return ToolResult(
                call_id=call.call_id,
                success=False,
                error=f"Unknown tool: {tool_name}"
            )
        
        try:
            # Get the handler method
            handler_name = f"_handle_{tool_name}"
            handler = getattr(self, handler_name, None)
            
            if not handler:
                return ToolResult(
                    call_id=call.call_id,
                    success=False,
                    error=f"No handler for tool: {tool_name}"
                )
            
            # Execute the handler
            result = await handler(call.parameters)
            
            return ToolResult(
                call_id=call.call_id,
                success=True,
                result=result,
                metadata={"execution_time": time.perf_counter()}
            )
            
        except Exception as e:
            self.logger.error(
                "Tool execution error",
                tool=tool_name,
                error=str(e)
            )
            return ToolResult(
                call_id=call.call_id,
                success=False,
                error=f"Tool execution failed: {e}"
            )


class ScreenCaptureTools(BaseMCPTools):
    """Screen capture tools for MCP."""
    
    def __init__(self):
        self.screen_capture = ScreenCapture()
        self.data_encoder = DataEncoder()
        super().__init__()
    
    def _register_tools(self) -> None:
        """Register screen capture tools."""
        self.tools["capture_screen"] = ToolDefinition(
            name="capture_screen",
            description="Capture current screen content and return as base64 encoded image",
            parameters=[
                ToolParameter(
                    name="monitor",
                    type="integer",
                    description="Monitor index to capture (0 for primary)",
                    required=False,
                    default=0
                ),
                ToolParameter(
                    name="region",
                    type="array",
                    description="Capture region as [x, y, width, height]",
                    required=False
                ),
                ToolParameter(
                    name="format",
                    type="string",
                    description="Output format",
                    required=False,
                    default="base64_jpeg",
                    enum=["base64_jpeg", "base64_png", "base64_webp"]
                ),
                ToolParameter(
                    name="quality",
                    type="integer",
                    description="Image quality (1-100)",
                    required=False,
                    default=85
                )
            ],
            returns="Base64 encoded screen image with metadata"
        )
        
        self.tools["get_monitors"] = ToolDefinition(
            name="get_monitors",
            description="Get list of available monitors",
            parameters=[],
            returns="List of available monitors with their properties"
        )
        
        self.tools["capture_region"] = ToolDefinition(
            name="capture_region",
            description="Capture a specific region of the screen",
            parameters=[
                ToolParameter(
                    name="x",
                    type="integer",
                    description="X coordinate of region",
                    required=True
                ),
                ToolParameter(
                    name="y",
                    type="integer",
                    description="Y coordinate of region",
                    required=True
                ),
                ToolParameter(
                    name="width",
                    type="integer",
                    description="Width of region",
                    required=True
                ),
                ToolParameter(
                    name="height",
                    type="integer",
                    description="Height of region",
                    required=True
                ),
                ToolParameter(
                    name="format",
                    type="string",
                    description="Output format",
                    required=False,
                    default="base64_jpeg",
                    enum=["base64_jpeg", "base64_png", "base64_webp"]
                ),
                ToolParameter(
                    name="quality",
                    type="integer",
                    description="Image quality (1-100)",
                    required=False,
                    default=85
                )
            ],
            returns="Base64 encoded region image with metadata"
        )
    
    async def _handle_capture_screen(self, params: Dict[str, Any]) -> ScreenCaptureResult:
        """Handle screen capture request."""
        try:
            # Parse parameters
            capture_params = ScreenCaptureParams(**params)
            
            # Set encoder format
            format_map = {
                "base64_jpeg": EncodingFormat.BASE64_JPEG,
                "base64_png": EncodingFormat.BASE64_PNG,
                "base64_webp": EncodingFormat.BASE64_WEBP,
            }
            
            self.data_encoder.format = format_map.get(
                capture_params.format, EncodingFormat.BASE64_JPEG
            )
            self.data_encoder.quality = capture_params.quality
            
            # Capture screen
            region = tuple(capture_params.region) if capture_params.region else None
            frame, metadata = await self.screen_capture.capture_frame(
                monitor=capture_params.monitor,
                region=region,
                format_output="RGB"
            )
            
            # Encode frame
            encoded_data, encoding_metadata = await self.data_encoder.encode_frame(
                frame, metadata
            )
            
            return ScreenCaptureResult(
                success=True,
                data=encoded_data,
                metadata={
                    **metadata,
                    **encoding_metadata,
                    "tool": "capture_screen"
                }
            )
            
        except Exception as e:
            self.logger.error("Screen capture failed", error=str(e))
            return ScreenCaptureResult(
                success=False,
                error=str(e)
            )
    
    async def _handle_get_monitors(self, params: Dict[str, Any]) -> SystemInfoResult:
        """Handle get monitors request."""
        try:
            monitors = await self.screen_capture.get_monitors()
            
            return SystemInfoResult(
                success=True,
                monitors=monitors,
                metadata={"tool": "get_monitors"}
            )
            
        except Exception as e:
            self.logger.error("Get monitors failed", error=str(e))
            return SystemInfoResult(
                success=False,
                error=str(e)
            )
    
    async def _handle_capture_region(self, params: Dict[str, Any]) -> ScreenCaptureResult:
        """Handle region capture request."""
        try:
            # Extract region parameters
            x = params["x"]
            y = params["y"]
            width = params["width"]
            height = params["height"]
            region = (x, y, width, height)
            
            # Create capture params
            capture_params = ScreenCaptureParams(
                region=list(region),
                format=params.get("format", "base64_jpeg"),
                quality=params.get("quality", 85)
            )
            
            # Use the same logic as capture_screen
            return await self._handle_capture_screen(capture_params.model_dump())
            
        except Exception as e:
            self.logger.error("Region capture failed", error=str(e))
            return ScreenCaptureResult(
                success=False,
                error=str(e)
            )


class StreamingTools(BaseMCPTools):
    """Streaming tools for MCP."""
    
    def __init__(self):
        self.ai_interface = AIInterface()
        self.screen_streamer: Optional[ScreenStreamer] = None
        super().__init__()
    
    def _register_tools(self) -> None:
        """Register streaming tools."""
        self.tools["start_vision_stream"] = ToolDefinition(
            name="start_vision_stream",
            description="Start real-time screen streaming for AI vision",
            parameters=[
                ToolParameter(
                    name="session_id",
                    type="string",
                    description="Unique session identifier",
                    required=True
                ),
                ToolParameter(
                    name="client_type",
                    type="string",
                    description="AI client type for optimization",
                    required=False,
                    default="claude",
                    enum=["claude", "gpt", "gemini", "custom"]
                ),
                ToolParameter(
                    name="fps",
                    type="integer",
                    description="Frames per second",
                    required=False,
                    default=5
                ),
                ToolParameter(
                    name="encoding_format",
                    type="string",
                    description="Encoding format",
                    required=False,
                    default="base64_jpeg",
                    enum=["base64_jpeg", "base64_png", "base64_webp"]
                ),
                ToolParameter(
                    name="quality",
                    type="integer",
                    description="Image quality (1-100)",
                    required=False,
                    default=75
                ),
                ToolParameter(
                    name="monitor",
                    type="integer",
                    description="Monitor to capture",
                    required=False,
                    default=0
                )
            ],
            returns="Streaming session information"
        )
        
        self.tools["stop_vision_stream"] = ToolDefinition(
            name="stop_vision_stream",
            description="Stop AI vision streaming session",
            parameters=[
                ToolParameter(
                    name="session_id",
                    type="string",
                    description="Session ID to stop",
                    required=True
                )
            ],
            returns="Session stop confirmation and statistics"
        )
        
        self.tools["get_stream_status"] = ToolDefinition(
            name="get_stream_status",
            description="Get current streaming status and statistics",
            parameters=[
                ToolParameter(
                    name="session_id",
                    type="string",
                    description="Session ID to check",
                    required=False
                )
            ],
            returns="Streaming status and statistics"
        )
    
    async def _handle_start_vision_stream(self, params: Dict[str, Any]) -> StreamingResult:
        """Handle start vision stream request."""
        try:
            # Create AI request
            ai_request = {
                "action": "start_vision",
                "session_id": params["session_id"],
                "client_type": params.get("client_type", "claude"),
                "settings": {
                    "fps": params.get("fps", 5),
                    "encoding_format": params.get("encoding_format", "base64_jpeg"),
                    "quality": params.get("quality", 75),
                    "monitor": params.get("monitor", 0)
                }
            }
            
            # Handle through AI interface
            response = await self.ai_interface.handle_ai_request(ai_request)
            
            if response["status"] == "success":
                return StreamingResult(
                    success=True,
                    session_id=params["session_id"],
                    status="started",
                    metadata=response.get("data", {})
                )
            else:
                return StreamingResult(
                    success=False,
                    error=response.get("error", "Unknown error")
                )
                
        except Exception as e:
            self.logger.error("Start vision stream failed", error=str(e))
            return StreamingResult(
                success=False,
                error=str(e)
            )
    
    async def _handle_stop_vision_stream(self, params: Dict[str, Any]) -> StreamingResult:
        """Handle stop vision stream request."""
        try:
            ai_request = {
                "action": "stop_vision",
                "session_id": params["session_id"]
            }
            
            response = await self.ai_interface.handle_ai_request(ai_request)
            
            if response["status"] == "success":
                return StreamingResult(
                    success=True,
                    session_id=params["session_id"],
                    status="stopped",
                    metadata=response.get("data", {})
                )
            else:
                return StreamingResult(
                    success=False,
                    error=response.get("error", "Unknown error")
                )
                
        except Exception as e:
            self.logger.error("Stop vision stream failed", error=str(e))
            return StreamingResult(
                success=False,
                error=str(e)
            )
    
    async def _handle_get_stream_status(self, params: Dict[str, Any]) -> StreamingResult:
        """Handle get stream status request."""
        try:
            session_id = params.get("session_id")
            
            if session_id:
                # Get specific session info
                ai_request = {
                    "action": "get_session_info",
                    "session_id": session_id
                }
                response = await self.ai_interface.handle_ai_request(ai_request)
            else:
                # Get all sessions info
                stats = self.ai_interface.get_stats()
                response = {
                    "status": "success",
                    "data": stats
                }
            
            if response["status"] == "success":
                return StreamingResult(
                    success=True,
                    session_id=session_id,
                    status="info_retrieved",
                    metadata=response.get("data", {})
                )
            else:
                return StreamingResult(
                    success=False,
                    error=response.get("error", "Unknown error")
                )
                
        except Exception as e:
            self.logger.error("Get stream status failed", error=str(e))
            return StreamingResult(
                success=False,
                error=str(e)
            )


class SystemTools(BaseMCPTools):
    """System information tools for MCP."""
    
    def _register_tools(self) -> None:
        """Register system tools."""
        self.tools["get_system_info"] = ToolDefinition(
            name="get_system_info",
            description="Get system information and capabilities",
            parameters=[
                ToolParameter(
                    name="include_monitors",
                    type="boolean",
                    description="Include monitor information",
                    required=False,
                    default=True
                ),
                ToolParameter(
                    name="include_performance",
                    type="boolean",
                    description="Include performance metrics",
                    required=False,
                    default=False
                )
            ],
            returns="System information and capabilities"
        )
    
    async def _handle_get_system_info(self, params: Dict[str, Any]) -> SystemInfoResult:
        """Handle get system info request."""
        try:
            import platform
            import psutil
            
            system_info = {
                "platform": platform.system(),
                "platform_version": platform.version(),
                "architecture": platform.architecture()[0],
                "processor": platform.processor(),
                "python_version": platform.python_version(),
                "matrix_vision_version": "0.1.0",
                "capabilities": {
                    "screen_capture": True,
                    "streaming": True,
                    "multiple_monitors": True,
                    "region_capture": True,
                    "ai_optimized": True
                }
            }
            
            monitors = None
            if params.get("include_monitors", True):
                try:
                    screen_capture = ScreenCapture()
                    await screen_capture.initialize()
                    monitors = await screen_capture.get_monitors()
                    await screen_capture.cleanup()
                except Exception as e:
                    self.logger.warning("Failed to get monitors", error=str(e))
            
            performance = None
            if params.get("include_performance", False):
                try:
                    performance = {
                        "cpu_percent": psutil.cpu_percent(),
                        "memory_percent": psutil.virtual_memory().percent,
                        "disk_usage": psutil.disk_usage('/').percent if platform.system() != "Windows" else psutil.disk_usage('C:').percent
                    }
                except Exception as e:
                    self.logger.warning("Failed to get performance metrics", error=str(e))
            
            return SystemInfoResult(
                success=True,
                system_info=system_info,
                monitors=monitors,
                performance=performance,
                metadata={"tool": "get_system_info"}
            )
            
        except Exception as e:
            self.logger.error("Get system info failed", error=str(e))
            return SystemInfoResult(
                success=False,
                error=str(e)
            )
