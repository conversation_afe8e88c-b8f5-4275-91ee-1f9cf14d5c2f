"""Data encoding utilities for screen streaming to AI clients."""

import asyncio
import base64
import io
import time
from enum import Enum
from typing import Op<PERSON>, Tuple, Union

import numpy as np
from PIL import Image

from ..core.config import settings
from ..core.exceptions import PerformanceError
from ..core.logger import LoggerMixin, PerformanceLogger


class EncodingFormat(Enum):
    """Supported encoding formats for screen data."""
    
    BASE64_JPEG = "base64_jpeg"
    BASE64_PNG = "base64_png"
    BASE64_WEBP = "base64_webp"
    RAW_BYTES = "raw_bytes"
    COMPRESSED_NUMPY = "compressed_numpy"


class DataEncoder(LoggerMixin):
    """Encodes screen data for efficient transmission to AI clients."""
    
    def __init__(
        self,
        format: EncodingFormat = EncodingFormat.BASE64_JPEG,
        quality: int = 85,
        compression_level: int = 6
    ):
        self.format = format
        self.quality = quality
        self.compression_level = compression_level
        self._perf_logger = PerformanceLogger(self.logger)
        
        # Encoding statistics
        self._stats = {
            "frames_encoded": 0,
            "total_input_bytes": 0,
            "total_output_bytes": 0,
            "total_encoding_time": 0.0,
            "avg_compression_ratio": 0.0,
        }
    
    async def encode_frame(
        self,
        frame: np.ndarray,
        metadata: Optional[dict] = None
    ) -> Tuple[str, dict]:
        """
        Encode a screen frame for AI consumption.
        
        Args:
            frame: Screen frame as numpy array (H, W, C) or (H, W)
            metadata: Optional frame metadata
            
        Returns:
            Tuple of (encoded_data, encoding_metadata)
        """
        self._perf_logger.start_timer("encode_frame")
        
        try:
            # Calculate input size
            input_size = frame.nbytes
            
            # Encode based on format
            if self.format == EncodingFormat.BASE64_JPEG:
                encoded_data = await self._encode_base64_jpeg(frame)
            elif self.format == EncodingFormat.BASE64_PNG:
                encoded_data = await self._encode_base64_png(frame)
            elif self.format == EncodingFormat.BASE64_WEBP:
                encoded_data = await self._encode_base64_webp(frame)
            elif self.format == EncodingFormat.RAW_BYTES:
                encoded_data = await self._encode_raw_bytes(frame)
            elif self.format == EncodingFormat.COMPRESSED_NUMPY:
                encoded_data = await self._encode_compressed_numpy(frame)
            else:
                raise ValueError(f"Unsupported encoding format: {self.format}")
            
            # Calculate output size and compression ratio
            output_size = len(encoded_data.encode('utf-8')) if isinstance(encoded_data, str) else len(encoded_data)
            compression_ratio = input_size / output_size if output_size > 0 else 0
            
            # Create encoding metadata
            encoding_metadata = {
                "format": self.format.value,
                "quality": self.quality,
                "compression_level": self.compression_level,
                "input_size_bytes": input_size,
                "output_size_bytes": output_size,
                "compression_ratio": round(compression_ratio, 2),
                "encoding_time_ms": round(self._perf_logger.end_timer("encode_frame") * 1000, 2),
                "timestamp": time.perf_counter(),
                "frame_shape": frame.shape,
                "frame_dtype": str(frame.dtype),
            }
            
            # Add original metadata
            if metadata:
                encoding_metadata.update(metadata)
            
            # Update statistics
            self._update_stats(input_size, output_size, encoding_metadata["encoding_time_ms"] / 1000)
            
            return encoded_data, encoding_metadata
            
        except Exception as e:
            self._perf_logger.end_timer("encode_frame")
            raise PerformanceError(f"Frame encoding failed: {e}") from e
    
    async def _encode_base64_jpeg(self, frame: np.ndarray) -> str:
        """Encode frame as base64 JPEG."""
        loop = asyncio.get_event_loop()
        
        def _encode():
            # Convert to PIL Image
            if len(frame.shape) == 3:
                image = Image.fromarray(frame, 'RGB')
            else:
                image = Image.fromarray(frame, 'L')
            
            # Save as JPEG
            buffer = io.BytesIO()
            image.save(buffer, format='JPEG', quality=self.quality, optimize=True)
            
            # Encode to base64
            return base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return await loop.run_in_executor(None, _encode)
    
    async def _encode_base64_png(self, frame: np.ndarray) -> str:
        """Encode frame as base64 PNG."""
        loop = asyncio.get_event_loop()
        
        def _encode():
            # Convert to PIL Image
            if len(frame.shape) == 3:
                image = Image.fromarray(frame, 'RGB')
            else:
                image = Image.fromarray(frame, 'L')
            
            # Save as PNG
            buffer = io.BytesIO()
            image.save(buffer, format='PNG', compress_level=self.compression_level)
            
            # Encode to base64
            return base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return await loop.run_in_executor(None, _encode)
    
    async def _encode_base64_webp(self, frame: np.ndarray) -> str:
        """Encode frame as base64 WebP."""
        loop = asyncio.get_event_loop()
        
        def _encode():
            # Convert to PIL Image
            if len(frame.shape) == 3:
                image = Image.fromarray(frame, 'RGB')
            else:
                image = Image.fromarray(frame, 'L')
            
            # Save as WebP
            buffer = io.BytesIO()
            image.save(buffer, format='WEBP', quality=self.quality, method=6)
            
            # Encode to base64
            return base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return await loop.run_in_executor(None, _encode)
    
    async def _encode_raw_bytes(self, frame: np.ndarray) -> str:
        """Encode frame as raw bytes (base64 encoded)."""
        loop = asyncio.get_event_loop()
        
        def _encode():
            # Convert numpy array to bytes
            frame_bytes = frame.tobytes()
            
            # Encode to base64
            return base64.b64encode(frame_bytes).decode('utf-8')
        
        return await loop.run_in_executor(None, _encode)
    
    async def _encode_compressed_numpy(self, frame: np.ndarray) -> str:
        """Encode frame as compressed numpy array."""
        loop = asyncio.get_event_loop()
        
        def _encode():
            import gzip
            
            # Serialize numpy array
            buffer = io.BytesIO()
            np.save(buffer, frame)
            
            # Compress
            compressed = gzip.compress(buffer.getvalue(), compresslevel=self.compression_level)
            
            # Encode to base64
            return base64.b64encode(compressed).decode('utf-8')
        
        return await loop.run_in_executor(None, _encode)
    
    def _update_stats(self, input_size: int, output_size: int, encoding_time: float) -> None:
        """Update encoding statistics."""
        self._stats["frames_encoded"] += 1
        self._stats["total_input_bytes"] += input_size
        self._stats["total_output_bytes"] += output_size
        self._stats["total_encoding_time"] += encoding_time
        
        # Calculate average compression ratio
        if self._stats["total_output_bytes"] > 0:
            self._stats["avg_compression_ratio"] = (
                self._stats["total_input_bytes"] / self._stats["total_output_bytes"]
            )
    
    def get_stats(self) -> dict:
        """Get encoding statistics."""
        stats = self._stats.copy()
        
        if stats["frames_encoded"] > 0:
            stats["avg_encoding_time_ms"] = round(
                (stats["total_encoding_time"] / stats["frames_encoded"]) * 1000, 2
            )
            stats["avg_input_size_kb"] = round(
                stats["total_input_bytes"] / stats["frames_encoded"] / 1024, 2
            )
            stats["avg_output_size_kb"] = round(
                stats["total_output_bytes"] / stats["frames_encoded"] / 1024, 2
            )
        else:
            stats["avg_encoding_time_ms"] = 0
            stats["avg_input_size_kb"] = 0
            stats["avg_output_size_kb"] = 0
        
        return stats
    
    async def set_format(self, format: EncodingFormat) -> None:
        """Change encoding format."""
        self.format = format
        self.logger.info("Encoding format changed", format=format.value)
    
    async def set_quality(self, quality: int) -> None:
        """Change encoding quality."""
        self.quality = max(1, min(100, quality))
        self.logger.info("Encoding quality changed", quality=self.quality)
    
    async def optimize_for_ai_client(self, client_type: str = "claude") -> None:
        """Optimize encoding settings for specific AI client."""
        if client_type.lower() == "claude":
            # Claude Desktop works well with JPEG at moderate quality
            self.format = EncodingFormat.BASE64_JPEG
            self.quality = 75
            self.compression_level = 6
        elif client_type.lower() == "gpt":
            # GPT might prefer PNG for better quality
            self.format = EncodingFormat.BASE64_PNG
            self.compression_level = 6
        else:
            # Default settings
            self.format = EncodingFormat.BASE64_JPEG
            self.quality = 85
            self.compression_level = 6
        
        self.logger.info(
            "Encoding optimized for AI client",
            client_type=client_type,
            format=self.format.value,
            quality=self.quality
        )
