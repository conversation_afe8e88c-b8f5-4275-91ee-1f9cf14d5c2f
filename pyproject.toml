[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "matrix-vision"
version = "0.1.0"
description = "AI-powered Matrix digital rain effect with real-time screen capture streaming"
authors = [
    {name = "inkbytefo", email = "<EMAIL>"},
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.9"
keywords = ["matrix", "screen-capture", "websocket", "ai", "streaming"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Multimedia :: Graphics :: Capture :: Screen Capture",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
]

dependencies = [
    # Core dependencies
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "websockets>=12.0",
    
    # Screen capture and image processing
    "mss>=9.0.1",  # Modern screen capture library
    "pillow>=10.0.0",
    "numpy>=1.24.0",
    "opencv-python>=4.8.0",
    
    # Async and performance
    "asyncio-throttle>=1.0.2",
    "aiofiles>=23.0.0",
    
    # Configuration and logging
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "structlog>=23.0.0",
    "rich>=13.0.0",
    
    # MCP Protocol
    "mcp>=1.0.0",
    "httpx>=0.25.0",
    
    # Optional GPU acceleration
    "cupy-cuda12x>=12.0.0; platform_machine=='x86_64'",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.4.0",
]

gui = [
    "customtkinter>=5.2.0",
    "tkinter-tooltip>=2.1.0",
]

web = [
    "jinja2>=3.1.0",
    "aiofiles>=23.0.0",
]

[project.urls]
Homepage = "https://github.com/inkbytefo/matrix-vision"
Repository = "https://github.com/inkbytefo/matrix-vision"
Issues = "https://github.com/inkbytefo/matrix-vision/issues"

[project.scripts]
matrix-vision = "matrix_vision.cli:main"
matrix-server = "matrix_vision.server.main:run_server"
matrix-client = "matrix_vision.client.main:run_client"

[tool.hatch.build.targets.wheel]
packages = ["src/matrix_vision"]

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/tests",
    "/docs",
    "/README.md",
    "/LICENSE",
]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["matrix_vision"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "mss.*",
    "cv2.*",
    "cupy.*",
    "customtkinter.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=matrix_vision",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "gpu: marks tests that require GPU",
]

[tool.coverage.run]
source = ["src/matrix_vision"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
