"""Example MCP client for testing Matrix Vision MCP server."""

import asyncio
import json
import sys
from typing import Any, Dict, Optional


class MCPClient:
    """Simple MCP client for testing."""
    
    def __init__(self):
        self.request_id = 0
        self.initialized = False
    
    def _next_id(self) -> int:
        """Get next request ID."""
        self.request_id += 1
        return self.request_id
    
    def _create_request(self, method: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Create MCP request."""
        request = {
            "jsonrpc": "2.0",
            "method": method,
            "id": self._next_id()
        }
        
        if params:
            request["params"] = params
        
        return request
    
    async def send_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Send request to MCP server via stdio."""
        # Send request
        print(json.dumps(request), flush=True)
        
        # Read response
        response_line = await asyncio.get_event_loop().run_in_executor(
            None, sys.stdin.readline
        )
        
        if not response_line:
            raise Exception("No response from server")
        
        return json.loads(response_line.strip())
    
    async def initialize(self) -> bool:
        """Initialize MCP connection."""
        request = self._create_request("initialize", {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "roots": [],
                "sampling": False
            },
            "clientInfo": {
                "name": "matrix-vision-test-client",
                "version": "0.1.0"
            }
        })
        
        response = await self.send_request(request)
        
        if "error" in response:
            print(f"Initialize error: {response['error']}")
            return False
        
        print(f"Server initialized: {response['result']}")
        
        # Send initialized notification
        notification = {
            "jsonrpc": "2.0",
            "method": "notifications/initialized"
        }
        print(json.dumps(notification), flush=True)
        
        self.initialized = True
        return True
    
    async def list_tools(self) -> Dict[str, Any]:
        """List available tools."""
        request = self._create_request("tools/list")
        response = await self.send_request(request)
        
        if "error" in response:
            print(f"List tools error: {response['error']}")
            return {}
        
        return response["result"]
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any] = None) -> Dict[str, Any]:
        """Call a tool."""
        request = self._create_request("tools/call", {
            "name": tool_name,
            "arguments": arguments or {}
        })
        
        response = await self.send_request(request)
        
        if "error" in response:
            print(f"Tool call error: {response['error']}")
            return {}
        
        return response["result"]


async def test_screen_capture():
    """Test screen capture tools."""
    print("=== Testing Screen Capture ===")
    
    client = MCPClient()
    
    try:
        # Initialize
        if not await client.initialize():
            return
        
        # List tools
        tools_result = await client.list_tools()
        print(f"Available tools: {len(tools_result.get('tools', []))}")
        
        for tool in tools_result.get('tools', []):
            print(f"  - {tool['name']}: {tool['description']}")
        
        # Test get_monitors
        print("\n--- Testing get_monitors ---")
        monitors_result = await client.call_tool("get_monitors")
        print(f"Monitors result: {monitors_result}")
        
        # Test capture_screen
        print("\n--- Testing capture_screen ---")
        capture_result = await client.call_tool("capture_screen", {
            "monitor": 0,
            "format": "base64_jpeg",
            "quality": 75
        })
        
        if capture_result.get("content"):
            for content in capture_result["content"]:
                if content["type"] == "text":
                    print(f"Text: {content['text']}")
                elif content["type"] == "image":
                    print(f"Image received: {len(content['data'])} characters")
        
        # Test capture_region
        print("\n--- Testing capture_region ---")
        region_result = await client.call_tool("capture_region", {
            "x": 0,
            "y": 0,
            "width": 800,
            "height": 600,
            "format": "base64_png"
        })
        
        if region_result.get("content"):
            for content in region_result["content"]:
                if content["type"] == "text":
                    print(f"Text: {content['text']}")
                elif content["type"] == "image":
                    print(f"Region image received: {len(content['data'])} characters")
    
    except Exception as e:
        print(f"Test error: {e}")


async def test_streaming():
    """Test streaming tools."""
    print("\n=== Testing Streaming ===")
    
    client = MCPClient()
    
    try:
        # Initialize
        if not await client.initialize():
            return
        
        # Start vision stream
        print("\n--- Starting vision stream ---")
        start_result = await client.call_tool("start_vision_stream", {
            "session_id": "test-session-123",
            "client_type": "claude",
            "fps": 5,
            "encoding_format": "base64_jpeg",
            "quality": 75
        })
        print(f"Start stream result: {start_result}")
        
        # Get stream status
        print("\n--- Getting stream status ---")
        status_result = await client.call_tool("get_stream_status", {
            "session_id": "test-session-123"
        })
        print(f"Stream status: {status_result}")
        
        # Wait a bit
        await asyncio.sleep(2)
        
        # Stop vision stream
        print("\n--- Stopping vision stream ---")
        stop_result = await client.call_tool("stop_vision_stream", {
            "session_id": "test-session-123"
        })
        print(f"Stop stream result: {stop_result}")
    
    except Exception as e:
        print(f"Streaming test error: {e}")


async def test_system_info():
    """Test system info tools."""
    print("\n=== Testing System Info ===")
    
    client = MCPClient()
    
    try:
        # Initialize
        if not await client.initialize():
            return
        
        # Get system info
        print("\n--- Getting system info ---")
        system_result = await client.call_tool("get_system_info", {
            "include_monitors": True,
            "include_performance": True
        })
        print(f"System info result: {system_result}")
    
    except Exception as e:
        print(f"System info test error: {e}")


async def main():
    """Run all MCP tests."""
    print("Matrix Vision MCP Client Test")
    print("=" * 50)
    print("Make sure to start the MCP server first:")
    print("  matrix-vision mcp")
    print("=" * 50)
    
    try:
        await test_screen_capture()
        await test_streaming()
        await test_system_info()
        
        print("\n" + "=" * 50)
        print("All MCP tests completed!")
        
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
    except Exception as e:
        print(f"\nTest error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
