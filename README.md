# Matrix Vision

AI-powered real-time screen streaming system that enables AI models (like <PERSON>) to "see" your screen through encoded data streams.

## 🎯 Project Purpose

Matrix Vision creates a bridge between your computer screen and AI models by:

1. **Real-time Screen Capture**: Continuously captures your screen content
2. **Base64 Encoding**: Converts screen data to base64 format for AI consumption  
3. **WebSocket Streaming**: Streams encoded data to AI clients in real-time
4. **MCP Integration**: Provides Model Context Protocol tools for AI interaction
5. **AI Optimization**: Optimizes encoding for different AI clients (Claude, GPT, etc.)

The "Matrix effect" refers to AI models being able to see the encoded screen data as "code" - similar to how characters in The Matrix could see the code behind reality.

## 🚀 Features

- **High-Performance Screen Capture**: Uses MSS library for fast, cross-platform screen capture
- **Multiple Encoding Formats**: JPEG, PNG, WebP with quality control
- **WebSocket Server**: Real-time streaming with connection management
- **MCP Protocol Support**: Standard interface for AI model integration
- **Multi-Monitor Support**: Capture from specific monitors or regions
- **Rate Limiting**: FPS control and bandwidth optimization
- **AI Client Optimization**: Automatic settings for different AI models
- **Comprehensive Logging**: Structured logging with performance metrics

## 📦 Installation

### Prerequisites

- Python 3.9 or higher
- Windows, macOS, or Linux

### Install from Source

```bash
# Clone the repository
git clone https://github.com/inkbytefo/matrix-vision.git
cd matrix-vision

# Install in development mode
pip install -e .

# Or install with optional dependencies
pip install -e ".[dev,gui,web]"
```

### Install Dependencies

```bash
# Core dependencies
pip install mss pillow numpy fastapi uvicorn websockets

# Optional for better performance
pip install opencv-python

# For development
pip install pytest black isort mypy
```

## 🎮 Usage

### Command Line Interface

Matrix Vision provides a comprehensive CLI:

```bash
# Show help
matrix-vision --help

# Start WebSocket server
matrix-vision server --host localhost --port 8765

# Start MCP server for AI integration
matrix-vision mcp

# Capture single screenshot
matrix-vision capture --output screenshot.jpg --monitor 0

# List available monitors
matrix-vision monitors

# Start AI vision streaming
matrix-vision stream --session-id my-session --client-type claude --fps 5

# Show system status
matrix-vision status
```

### MCP Integration with Claude Desktop

1. **Start MCP Server**:
```bash
matrix-vision mcp
```

2. **Configure Claude Desktop** (add to your MCP config):
```json
{
  "mcpServers": {
    "matrix-vision": {
      "command": "matrix-vision",
      "args": ["mcp"]
    }
  }
}
```

3. **Use in Claude Desktop**:
```
Can you capture my current screen?
Start streaming my screen for AI vision analysis.
```

### Python API

```python
import asyncio
from matrix_vision import ScreenCapture, ScreenStreamer, DataEncoder

async def main():
    # Single screen capture
    capture = ScreenCapture()
    await capture.initialize()
    
    frame, metadata = await capture.capture_frame()
    base64_data, _ = await capture.capture_frame_base64()
    
    print(f"Captured {frame.shape} frame")
    print(f"Base64 data: {len(base64_data)} characters")
    
    # Real-time streaming
    streamer = ScreenStreamer()
    await streamer.start_streaming(fps=10)
    
    # Subscribe to stream
    async def frame_callback(frame_data):
        print(f"Received frame: {len(frame_data['data'])} bytes")
    
    await streamer.subscribe("my-session", frame_callback)
    
    # Keep streaming
    await asyncio.sleep(10)
    await streamer.cleanup()

asyncio.run(main())
```

### WebSocket Client Example

```javascript
const ws = new WebSocket('ws://localhost:8765/ws');

ws.onopen = function() {
    // Start streaming
    ws.send(JSON.stringify({
        action: 'start_stream',
        fps: 10,
        effect_type: 'raw_screen',
        monitor: 0
    }));
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    
    if (data.type === 'stream_frame') {
        // Display base64 image
        const img = document.createElement('img');
        img.src = 'data:image/jpeg;base64,' + data.data;
        document.body.appendChild(img);
    }
};
```

## 🔧 Configuration

### Environment Variables

```bash
# Server configuration
MATRIX_VISION_SERVER__HOST=localhost
MATRIX_VISION_SERVER__PORT=8765
MATRIX_VISION_SERVER__MAX_CONNECTIONS=100

# Capture configuration  
MATRIX_VISION_CAPTURE__FPS=30
MATRIX_VISION_CAPTURE__QUALITY=85
MATRIX_VISION_CAPTURE__MAX_WIDTH=1920
MATRIX_VISION_CAPTURE__MAX_HEIGHT=1080

# Logging
MATRIX_VISION_LOGGING__LEVEL=INFO
MATRIX_VISION_DEBUG=false
```

### Configuration File

Create `.env` file or use Python configuration:

```python
from matrix_vision.core.config import settings

# Update settings
settings.capture.fps = 15
settings.capture.quality = 90
settings.server.port = 9000
```

## 🏗️ Architecture

```
Matrix Vision Architecture

┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   AI Clients    │    │  WebSocket       │    │ Screen Capture  │
│  (Claude, GPT)  │◄──►│     Server       │◄──►│    System       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         ▲                        ▲                       ▲
         │                        │                       │
         ▼                        ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  MCP Protocol   │    │  Connection      │    │  Data Encoder   │
│   Interface     │    │   Manager        │    │  (Base64/etc)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Core Components

- **Screen Capture**: High-performance screen grabbing with MSS/PIL
- **Data Encoder**: Converts frames to base64 with format optimization
- **WebSocket Server**: Real-time streaming with FastAPI
- **Connection Manager**: Client connection and session management
- **MCP Server**: Model Context Protocol for AI integration
- **Streaming Engine**: Frame distribution and rate limiting

## 🧪 Testing

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=matrix_vision

# Run specific test categories
pytest -m "not slow"  # Skip slow tests
pytest -m integration  # Only integration tests
```

## 📊 Performance

### Benchmarks

- **Screen Capture**: ~30-60 FPS (depending on resolution)
- **Encoding**: ~10-50ms per frame (JPEG quality 85)
- **Memory Usage**: ~50-200MB (depending on resolution and buffer size)
- **Network**: ~100KB-2MB per frame (depending on format and quality)

### Optimization Tips

1. **Lower FPS**: Use 5-10 FPS for AI vision (sufficient for most use cases)
2. **Reduce Quality**: 70-85 JPEG quality balances size and clarity
3. **Smaller Resolution**: Limit max width/height for better performance
4. **Choose Format**: JPEG for photos, PNG for text/UI, WebP for balance

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and add tests
4. Run tests: `pytest`
5. Format code: `black . && isort .`
6. Commit changes: `git commit -m 'Add amazing feature'`
7. Push to branch: `git push origin feature/amazing-feature`
8. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [MSS](https://github.com/BoboTiG/python-mss) for fast screen capture
- [FastAPI](https://fastapi.tiangolo.com/) for the WebSocket server
- [Anthropic](https://www.anthropic.com/) for the MCP protocol
- [Pillow](https://pillow.readthedocs.io/) for image processing

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/inkbytefo/matrix-vision/issues)
- 💬 Discussions: [GitHub Discussions](https://github.com/inkbytefo/matrix-vision/discussions)

---

**Matrix Vision** - Bridging the gap between human screens and AI vision 🤖👁️
