"""WebSocket server implementation for Matrix Vision."""

import async<PERSON>
import json
from typing import <PERSON><PERSON>

from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from ..core.config import settings
from ..core.exceptions import WebSocketError
from ..core.logger import LoggerMixin
from ..capture.screen_capture import CaptureManager
from ..streaming.screen_streamer import ScreenStreamer
from .connection_manager import ConnectionManager
from .handlers import <PERSON><PERSON><PERSON><PERSON>, StreamHandler


class MatrixVisionServer(LoggerMixin):
    """Main WebSocket server for Matrix Vision."""
    
    def __init__(self):
        self.app = FastAPI(
            title="Matrix Vision Server",
            description="AI-powered screen streaming WebSocket server",
            version="0.1.0"
        )
        
        # Add CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Initialize components
        self.connection_manager = ConnectionManager()
        self.capture_manager = CaptureManager()
        self.screen_streamer = ScreenStreamer(self.capture_manager)
        
        # Initialize handlers
        self.message_handler = MessageHandler(self.connection_manager)
        self.stream_handler = StreamHandler(self.connection_manager, self.capture_manager)
        
        # Setup routes
        self._setup_routes()
        
        self.is_running = False
    
    def _setup_routes(self) -> None:
        """Setup FastAPI routes."""
        
        @self.app.get("/")
        async def root():
            """Root endpoint."""
            return {
                "name": "Matrix Vision Server",
                "version": "0.1.0",
                "status": "running" if self.is_running else "stopped",
                "connections": len(self.connection_manager.connections),
                "streaming_clients": len(self.connection_manager.streaming_clients)
            }
        
        @self.app.get("/health")
        async def health():
            """Health check endpoint."""
            return {
                "status": "healthy",
                "server_running": self.is_running,
                "components": {
                    "connection_manager": len(self.connection_manager.connections),
                    "capture_manager": self.capture_manager.is_running,
                    "screen_streamer": self.screen_streamer.is_streaming
                }
            }
        
        @self.app.get("/stats")
        async def stats():
            """Server statistics endpoint."""
            return {
                "connection_stats": self.connection_manager.get_stats(),
                "capture_stats": self.capture_manager.stats,
                "streaming_stats": self.screen_streamer.get_stats()
            }
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """Main WebSocket endpoint."""
            await self._handle_websocket_connection(websocket)
    
    async def _handle_websocket_connection(self, websocket: WebSocket) -> None:
        """Handle new WebSocket connection."""
        client_id = None
        
        try:
            # Connect client
            client_id = await self.connection_manager.connect_client(websocket)
            
            self.logger.info(
                "WebSocket client connected",
                client_id=client_id,
                client_host=websocket.client.host if websocket.client else "unknown"
            )
            
            # Listen for messages
            while True:
                try:
                    # Receive message
                    message = await websocket.receive_text()
                    
                    # Handle message
                    await self.message_handler.handle_message(client_id, message)
                    
                except WebSocketDisconnect:
                    break
                except Exception as e:
                    self.logger.error(
                        "WebSocket message error",
                        client_id=client_id,
                        error=str(e)
                    )
                    # Send error to client
                    try:
                        await websocket.send_text(json.dumps({
                            "type": "error",
                            "error": f"Message processing error: {e}"
                        }))
                    except:
                        break  # Connection likely closed
        
        except WebSocketDisconnect:
            pass  # Normal disconnection
        except Exception as e:
            self.logger.error(
                "WebSocket connection error",
                client_id=client_id,
                error=str(e)
            )
        finally:
            # Cleanup client connection
            if client_id:
                await self.connection_manager.disconnect_client(
                    client_id, "Connection closed"
                )
    
    async def start(self) -> None:
        """Start the server."""
        try:
            self.is_running = True
            
            # Start background components
            await self.connection_manager.start()
            await self.stream_handler.start()
            
            self.logger.info(
                "Matrix Vision Server starting",
                host=settings.server.host,
                port=settings.server.port,
                max_connections=settings.server.max_connections
            )
            
            # Start uvicorn server
            config = uvicorn.Config(
                app=self.app,
                host=settings.server.host,
                port=settings.server.port,
                log_level="info" if not settings.debug else "debug",
                access_log=settings.debug,
                ws_ping_interval=settings.server.heartbeat_interval,
                ws_ping_timeout=settings.server.heartbeat_interval * 2,
            )
            
            server = uvicorn.Server(config)
            await server.serve()
            
        except Exception as e:
            self.logger.error("Server startup error", error=str(e))
            raise
        finally:
            await self.cleanup()
    
    async def cleanup(self) -> None:
        """Cleanup server resources."""
        self.is_running = False
        
        try:
            # Stop background components
            await self.stream_handler.stop()
            await self.connection_manager.stop()
            await self.capture_manager.cleanup()
            await self.screen_streamer.cleanup()
            
            self.logger.info("Matrix Vision Server cleaned up")
            
        except Exception as e:
            self.logger.error("Cleanup error", error=str(e))


# Standalone server runner
async def run_server(
    host: str = "localhost",
    port: int = 8765,
    debug: bool = False
) -> None:
    """Run the Matrix Vision server."""
    # Update settings
    settings.server.host = host
    settings.server.port = port
    settings.debug = debug
    
    # Create and start server
    server = MatrixVisionServer()
    await server.start()


if __name__ == "__main__":
    asyncio.run(run_server())
