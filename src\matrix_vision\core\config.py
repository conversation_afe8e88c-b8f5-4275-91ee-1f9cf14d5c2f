"""Configuration management for Matrix Vision."""

import os
from pathlib import Path
from typing import List, Optional, Union

from pydantic import BaseModel, Field, validator
from pydantic_settings import BaseSettings


class ServerConfig(BaseModel):
    """WebSocket server configuration."""
    
    host: str = Field(default="localhost", description="Server host")
    port: int = Field(default=8765, ge=1024, le=65535, description="Server port")
    max_connections: int = Field(default=100, ge=1, description="Maximum concurrent connections")
    heartbeat_interval: float = Field(default=30.0, ge=1.0, description="Heartbeat interval in seconds")
    compression: bool = Field(default=True, description="Enable WebSocket compression")


class CaptureConfig(BaseModel):
    """Screen capture configuration."""
    
    fps: int = Field(default=30, ge=1, le=120, description="Frames per second")
    quality: int = Field(default=85, ge=1, le=100, description="JPEG quality")
    max_width: int = Field(default=1920, ge=320, description="Maximum capture width")
    max_height: int = Field(default=1080, ge=240, description="Maximum capture height")
    monitor: int = Field(default=0, ge=0, description="Monitor index to capture")
    region: Optional[tuple[int, int, int, int]] = Field(default=None, description="Capture region (x, y, width, height)")
    
    @validator('region')
    def validate_region(cls, v):
        if v is not None and len(v) != 4:
            raise ValueError("Region must be a tuple of 4 integers (x, y, width, height)")
        return v


class EffectConfig(BaseModel):
    """Matrix effect configuration."""
    
    effect_type: str = Field(default="matrix_classic", description="Effect type")
    rain_density: float = Field(default=0.05, ge=0.001, le=1.0, description="Rain density")
    trail_length: int = Field(default=25, ge=5, le=100, description="Trail length")
    glow_intensity: float = Field(default=1.5, ge=0.1, le=5.0, description="Glow intensity")
    color_scheme: str = Field(default="green", description="Color scheme")
    animation_speed: float = Field(default=1.0, ge=0.1, le=5.0, description="Animation speed")
    use_gpu: bool = Field(default=False, description="Use GPU acceleration if available")


class LoggingConfig(BaseModel):
    """Logging configuration."""
    
    level: str = Field(default="INFO", description="Log level")
    format: str = Field(default="%(asctime)s - %(name)s - %(levelname)s - %(message)s", description="Log format")
    file_path: Optional[Path] = Field(default=None, description="Log file path")
    max_file_size: int = Field(default=10 * 1024 * 1024, description="Maximum log file size in bytes")
    backup_count: int = Field(default=5, description="Number of backup log files")
    
    @validator('level')
    def validate_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of {valid_levels}")
        return v.upper()


class MCPConfig(BaseModel):
    """MCP (Model Context Protocol) configuration."""
    
    enabled: bool = Field(default=True, description="Enable MCP integration")
    server_name: str = Field(default="matrix-vision", description="MCP server name")
    version: str = Field(default="1.0.0", description="MCP protocol version")
    capabilities: List[str] = Field(
        default=["screen_capture", "effect_control", "streaming"],
        description="Available capabilities"
    )


class Settings(BaseSettings):
    """Main application settings."""
    
    # Environment
    environment: str = Field(default="development", description="Environment (development/production)")
    debug: bool = Field(default=False, description="Debug mode")
    
    # Component configurations
    server: ServerConfig = Field(default_factory=ServerConfig)
    capture: CaptureConfig = Field(default_factory=CaptureConfig)
    effect: EffectConfig = Field(default_factory=EffectConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    mcp: MCPConfig = Field(default_factory=MCPConfig)
    
    # Paths
    data_dir: Path = Field(default=Path.home() / ".matrix_vision", description="Data directory")
    cache_dir: Path = Field(default=Path.home() / ".matrix_vision" / "cache", description="Cache directory")
    
    # Performance
    worker_threads: int = Field(default=4, ge=1, description="Number of worker threads")
    memory_limit_mb: int = Field(default=512, ge=128, description="Memory limit in MB")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        env_prefix = "MATRIX_VISION_"
        case_sensitive = False
        
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Create directories if they don't exist
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
    
    @validator('environment')
    def validate_environment(cls, v):
        valid_envs = ['development', 'production', 'testing']
        if v not in valid_envs:
            raise ValueError(f"Environment must be one of {valid_envs}")
        return v
    
    @property
    def is_development(self) -> bool:
        """Check if running in development mode."""
        return self.environment == "development"
    
    @property
    def is_production(self) -> bool:
        """Check if running in production mode."""
        return self.environment == "production"
    
    def get_log_file_path(self) -> Optional[Path]:
        """Get the log file path."""
        if self.logging.file_path:
            return self.logging.file_path
        if self.is_production:
            return self.data_dir / "matrix_vision.log"
        return None


# Global settings instance
settings = Settings()
