"""Message and stream handlers for WebSocket server."""

import asyncio
import json
import time
from typing import Any, Dict, Optional

from ..core.config import settings
from ..core.exceptions import WebSocketError, EffectError
from ..core.logger import LoggerMixin
from ..capture.screen_capture import Capture<PERSON>anager
from .connection_manager import ConnectionManager


class MessageHandler(LoggerMixin):
    """Handles incoming WebSocket messages."""
    
    def __init__(self, connection_manager: ConnectionManager):
        self.connection_manager = connection_manager
        self.supported_actions = {
            "start_stream": self._handle_start_stream,
            "stop_stream": self._handle_stop_stream,
            "change_settings": self._handle_change_settings,
            "get_status": self._handle_get_status,
            "get_monitors": self._handle_get_monitors,
            "ping": self._handle_ping,
        }
    
    async def handle_message(self, client_id: str, message: str) -> None:
        """
        Handle incoming message from client.
        
        Args:
            client_id: Client ID
            message: Raw message string
        """
        try:
            # Parse JSON message
            data = json.loads(message)
            
            # Update client activity
            client = self.connection_manager.get_client(client_id)
            if client:
                client.messages_received += 1
                client.bytes_received += len(message.encode('utf-8'))
                client.update_activity()
            
            # Extract action and request ID
            action = data.get("action")
            request_id = data.get("request_id")
            
            if action not in self.supported_actions:
                await self._send_error(
                    client_id,
                    f"Unknown action: {action}",
                    request_id=request_id
                )
                return
            
            # Handle the action
            handler = self.supported_actions[action]
            await handler(client_id, data, request_id)
            
        except json.JSONDecodeError:
            await self._send_error(client_id, "Invalid JSON format")
        except Exception as e:
            self.logger.error(
                "Message handling error",
                client_id=client_id,
                error=str(e)
            )
            await self._send_error(client_id, f"Internal error: {e}")
    
    async def _handle_start_stream(
        self,
        client_id: str,
        data: dict,
        request_id: Optional[str]
    ) -> None:
        """Handle start streaming request."""
        client = self.connection_manager.get_client(client_id)
        if not client:
            return
        
        # Update client settings
        settings_update = {}
        if "fps" in data:
            settings_update["fps"] = data["fps"]
        if "effect_type" in data:
            settings_update["effect_type"] = data["effect_type"]
        if "monitor" in data:
            settings_update["monitor"] = data["monitor"]
        if "region" in data:
            settings_update["region"] = data["region"]
        
        if settings_update:
            await self.connection_manager.update_client_settings(client_id, settings_update)
        
        # Start streaming
        success = await self.connection_manager.start_streaming(client_id)
        
        if success:
            await self._send_response(
                client_id,
                {
                    "status": "success",
                    "message": "Streaming started",
                    "settings": {
                        "fps": client.fps,
                        "effect_type": client.effect_type,
                        "monitor": client.monitor,
                        "region": client.region,
                    }
                },
                request_id=request_id
            )
        else:
            await self._send_error(
                client_id,
                "Failed to start streaming",
                request_id=request_id
            )
    
    async def _handle_stop_stream(
        self,
        client_id: str,
        data: dict,
        request_id: Optional[str]
    ) -> None:
        """Handle stop streaming request."""
        success = await self.connection_manager.stop_streaming(client_id)
        
        if success:
            await self._send_response(
                client_id,
                {"status": "success", "message": "Streaming stopped"},
                request_id=request_id
            )
        else:
            await self._send_error(
                client_id,
                "Failed to stop streaming",
                request_id=request_id
            )
    
    async def _handle_change_settings(
        self,
        client_id: str,
        data: dict,
        request_id: Optional[str]
    ) -> None:
        """Handle settings change request."""
        settings_data = data.get("settings", {})
        
        if not settings_data:
            await self._send_error(
                client_id,
                "No settings provided",
                request_id=request_id
            )
            return
        
        success = await self.connection_manager.update_client_settings(
            client_id, settings_data
        )
        
        if success:
            client = self.connection_manager.get_client(client_id)
            await self._send_response(
                client_id,
                {
                    "status": "success",
                    "message": "Settings updated",
                    "settings": {
                        "fps": client.fps,
                        "effect_type": client.effect_type,
                        "monitor": client.monitor,
                        "region": client.region,
                    }
                },
                request_id=request_id
            )
        else:
            await self._send_error(
                client_id,
                "Failed to update settings",
                request_id=request_id
            )
    
    async def _handle_get_status(
        self,
        client_id: str,
        data: dict,
        request_id: Optional[str]
    ) -> None:
        """Handle status request."""
        client = self.connection_manager.get_client(client_id)
        if not client:
            return
        
        await self._send_response(
            client_id,
            {
                "status": "success",
                "data": {
                    "client": client.to_dict(),
                    "server": self.connection_manager.get_stats()
                }
            },
            request_id=request_id
        )
    
    async def _handle_get_monitors(
        self,
        client_id: str,
        data: dict,
        request_id: Optional[str]
    ) -> None:
        """Handle get monitors request."""
        try:
            # This would need to be implemented with actual capture system
            monitors = [
                {"id": 0, "name": "Primary Monitor", "width": 1920, "height": 1080},
                {"id": 1, "name": "Secondary Monitor", "width": 1920, "height": 1080},
            ]
            
            await self._send_response(
                client_id,
                {"status": "success", "data": {"monitors": monitors}},
                request_id=request_id
            )
        except Exception as e:
            await self._send_error(
                client_id,
                f"Failed to get monitors: {e}",
                request_id=request_id
            )
    
    async def _handle_ping(
        self,
        client_id: str,
        data: dict,
        request_id: Optional[str]
    ) -> None:
        """Handle ping request."""
        await self._send_response(
            client_id,
            {
                "status": "success",
                "message": "pong",
                "timestamp": time.perf_counter()
            },
            request_id=request_id
        )
    
    async def _send_response(
        self,
        client_id: str,
        response: dict,
        request_id: Optional[str] = None
    ) -> None:
        """Send response to client."""
        message = {
            "type": "response",
            "timestamp": time.perf_counter(),
            **response
        }
        
        if request_id:
            message["request_id"] = request_id
        
        await self.connection_manager.send_message(client_id, message)
    
    async def _send_error(
        self,
        client_id: str,
        error_message: str,
        request_id: Optional[str] = None
    ) -> None:
        """Send error response to client."""
        message = {
            "type": "error",
            "timestamp": time.perf_counter(),
            "error": error_message
        }
        
        if request_id:
            message["request_id"] = request_id
        
        await self.connection_manager.send_message(client_id, message)


class StreamHandler(LoggerMixin):
    """Handles streaming of processed frames to clients."""
    
    def __init__(
        self,
        connection_manager: ConnectionManager,
        capture_manager: CaptureManager
    ):
        self.connection_manager = connection_manager
        self.capture_manager = capture_manager
        self.is_running = False
        self._stream_task: Optional[asyncio.Task] = None
    
    async def start(self) -> None:
        """Start the streaming handler."""
        if self.is_running:
            return
        
        self.is_running = True
        self._stream_task = asyncio.create_task(self._stream_loop())
        self.logger.info("Stream handler started")
    
    async def stop(self) -> None:
        """Stop the streaming handler."""
        if not self.is_running:
            return
        
        self.is_running = False
        
        if self._stream_task:
            self._stream_task.cancel()
            try:
                await self._stream_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Stream handler stopped")
    
    async def _stream_loop(self) -> None:
        """Main streaming loop."""
        while self.is_running:
            try:
                # Get streaming clients
                streaming_clients = self.connection_manager.get_streaming_clients()
                
                if not streaming_clients:
                    await asyncio.sleep(0.1)
                    continue
                
                # Get latest frame from capture manager
                frame_data = await self.capture_manager.get_latest_frame()
                
                if not frame_data:
                    await asyncio.sleep(0.01)
                    continue
                
                # Process and send frame to each streaming client
                await self._process_and_send_frame(frame_data, streaming_clients)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Stream loop error", error=str(e))
                await asyncio.sleep(0.1)
    
    async def _process_and_send_frame(
        self,
        frame_data: dict,
        clients: list
    ) -> None:
        """Process frame and send to clients."""
        frame = frame_data["frame"]
        timestamp = frame_data["timestamp"]
        
        # Group clients by their settings to optimize processing
        client_groups = {}
        
        for client in clients:
            key = (client.effect_type, client.fps)
            if key not in client_groups:
                client_groups[key] = []
            client_groups[key].append(client)
        
        # Process frame for each group
        for (effect_type, fps), group_clients in client_groups.items():
            try:
                # Apply effect (this would integrate with the effects module)
                processed_frame = await self._apply_effect(frame, effect_type)
                
                # Convert to base64
                base64_data = await self._frame_to_base64(processed_frame)
                
                # Create stream message
                stream_message = {
                    "type": "stream_frame",
                    "timestamp": timestamp,
                    "effect_type": effect_type,
                    "data": base64_data,
                    "frame_info": {
                        "width": processed_frame.shape[1],
                        "height": processed_frame.shape[0],
                        "format": "JPEG"
                    }
                }
                
                # Send to all clients in this group
                for client in group_clients:
                    # Check FPS throttling
                    if self._should_send_frame(client, fps):
                        await self.connection_manager.send_message(
                            client.id, stream_message
                        )
                
            except Exception as e:
                self.logger.error(
                    "Frame processing error",
                    effect_type=effect_type,
                    error=str(e)
                )
    
    async def _apply_effect(self, frame, effect_type: str):
        """No visual effects - just return raw screen data."""
        # The "Matrix effect" is actually the raw screen data itself
        # AI clients will see the encoded screen content as "code"
        return frame

    async def _frame_to_base64(self, frame) -> str:
        """Convert frame to base64 string for AI consumption."""
        from PIL import Image
        import base64
        import io

        # Convert numpy array to PIL Image
        if len(frame.shape) == 3:
            image = Image.fromarray(frame, 'RGB')
        else:
            image = Image.fromarray(frame, 'L')

        # Convert to base64
        buffer = io.BytesIO()
        image.save(buffer, format='JPEG', quality=settings.capture.quality)
        base64_string = base64.b64encode(buffer.getvalue()).decode('utf-8')

        return base64_string
    
    def _should_send_frame(self, client, target_fps: int) -> bool:
        """Check if frame should be sent based on FPS throttling."""
        current_time = time.perf_counter()
        
        # Simple FPS throttling
        if not hasattr(client, '_last_frame_time'):
            client._last_frame_time = 0
        
        frame_interval = 1.0 / target_fps
        if current_time - client._last_frame_time >= frame_interval:
            client._last_frame_time = current_time
            return True
        
        return False
