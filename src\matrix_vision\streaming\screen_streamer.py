"""Real-time screen streaming for AI clients."""

import asyncio
import time
from typing import Dict, List, Optional, Set

from ..capture.screen_capture import CaptureManager
from ..core.config import settings
from ..core.exceptions import PerformanceError
from ..core.logger import LoggerMixin, PerformanceLogger
from .data_encoder import DataEncoder, EncodingFormat


class ScreenStreamer(LoggerMixin):
    """Streams real-time screen data to AI clients."""
    
    def __init__(
        self,
        capture_manager: Optional[CaptureManager] = None,
        encoder: Optional[DataEncoder] = None
    ):
        self.capture_manager = capture_manager or CaptureManager()
        self.encoder = encoder or DataEncoder()
        
        # Streaming state
        self.is_streaming = False
        self._stream_task: Optional[asyncio.Task] = None
        self._subscribers: Dict[str, dict] = {}
        
        # Performance monitoring
        self._perf_logger = PerformanceLogger(self.logger)
        self._stats = {
            "stream_start_time": None,
            "frames_streamed": 0,
            "total_subscribers": 0,
            "active_subscribers": 0,
            "avg_fps": 0.0,
            "last_frame_time": 0.0,
        }
    
    async def start_streaming(
        self,
        monitor: int = None,
        region: Optional[tuple] = None,
        fps: int = None
    ) -> None:
        """
        Start screen streaming.
        
        Args:
            monitor: Monitor index to capture
            region: Capture region (x, y, width, height)
            fps: Target FPS for streaming
        """
        if self.is_streaming:
            self.logger.warning("Streaming already active")
            return
        
        # Use config defaults if not specified
        monitor = monitor if monitor is not None else settings.capture.monitor
        region = region or settings.capture.region
        fps = fps or settings.capture.fps
        
        # Start capture manager
        await self.capture_manager.start_capture(monitor, region)
        
        # Update encoder FPS
        await self.capture_manager.capture.frame_processor.set_target_fps(fps)
        
        # Start streaming
        self.is_streaming = True
        self._stats["stream_start_time"] = time.perf_counter()
        self._stream_task = asyncio.create_task(self._streaming_loop())
        
        self.logger.info(
            "Screen streaming started",
            monitor=monitor,
            region=region,
            fps=fps,
            encoding_format=self.encoder.format.value
        )
    
    async def stop_streaming(self) -> None:
        """Stop screen streaming."""
        if not self.is_streaming:
            return
        
        self.is_streaming = False
        
        # Cancel streaming task
        if self._stream_task:
            self._stream_task.cancel()
            try:
                await self._stream_task
            except asyncio.CancelledError:
                pass
        
        # Stop capture manager
        await self.capture_manager.stop_capture()
        
        # Clear subscribers
        self._subscribers.clear()
        
        self.logger.info("Screen streaming stopped")
    
    async def subscribe(
        self,
        subscriber_id: str,
        callback,
        encoding_format: Optional[EncodingFormat] = None,
        quality: Optional[int] = None
    ) -> bool:
        """
        Subscribe to screen stream.
        
        Args:
            subscriber_id: Unique subscriber ID
            callback: Async callback function to receive frames
            encoding_format: Preferred encoding format
            quality: Preferred quality (1-100)
            
        Returns:
            True if subscribed successfully
        """
        if subscriber_id in self._subscribers:
            self.logger.warning("Subscriber already exists", subscriber_id=subscriber_id)
            return False
        
        # Create subscriber info
        subscriber_info = {
            "id": subscriber_id,
            "callback": callback,
            "encoding_format": encoding_format or self.encoder.format,
            "quality": quality or self.encoder.quality,
            "subscribed_at": time.perf_counter(),
            "frames_received": 0,
            "last_frame_time": 0.0,
            "active": True,
        }
        
        self._subscribers[subscriber_id] = subscriber_info
        self._stats["total_subscribers"] += 1
        self._stats["active_subscribers"] = len(self._subscribers)
        
        self.logger.info(
            "New subscriber added",
            subscriber_id=subscriber_id,
            encoding_format=encoding_format.value if encoding_format else "default",
            total_subscribers=len(self._subscribers)
        )
        
        return True
    
    async def unsubscribe(self, subscriber_id: str) -> bool:
        """
        Unsubscribe from screen stream.
        
        Args:
            subscriber_id: Subscriber ID to remove
            
        Returns:
            True if unsubscribed successfully
        """
        if subscriber_id not in self._subscribers:
            return False
        
        subscriber_info = self._subscribers[subscriber_id]
        del self._subscribers[subscriber_id]
        
        self._stats["active_subscribers"] = len(self._subscribers)
        
        self.logger.info(
            "Subscriber removed",
            subscriber_id=subscriber_id,
            frames_received=subscriber_info["frames_received"],
            subscription_duration=time.perf_counter() - subscriber_info["subscribed_at"],
            remaining_subscribers=len(self._subscribers)
        )
        
        return True
    
    async def update_subscriber_settings(
        self,
        subscriber_id: str,
        encoding_format: Optional[EncodingFormat] = None,
        quality: Optional[int] = None
    ) -> bool:
        """Update subscriber settings."""
        if subscriber_id not in self._subscribers:
            return False
        
        subscriber = self._subscribers[subscriber_id]
        
        if encoding_format:
            subscriber["encoding_format"] = encoding_format
        if quality:
            subscriber["quality"] = quality
        
        self.logger.info(
            "Subscriber settings updated",
            subscriber_id=subscriber_id,
            encoding_format=encoding_format.value if encoding_format else "unchanged",
            quality=quality or "unchanged"
        )
        
        return True
    
    async def _streaming_loop(self) -> None:
        """Main streaming loop."""
        frame_count = 0
        last_fps_time = time.perf_counter()
        
        while self.is_streaming:
            try:
                # Get latest frame from capture manager
                frame_data = await self.capture_manager.get_latest_frame()
                
                if not frame_data:
                    await asyncio.sleep(0.01)  # Brief pause if no frame available
                    continue
                
                # Process frame for all subscribers
                await self._process_and_distribute_frame(frame_data)
                
                # Update statistics
                frame_count += 1
                self._stats["frames_streamed"] += 1
                self._stats["last_frame_time"] = time.perf_counter()
                
                # Calculate FPS every second
                current_time = time.perf_counter()
                if current_time - last_fps_time >= 1.0:
                    self._stats["avg_fps"] = frame_count / (current_time - last_fps_time)
                    frame_count = 0
                    last_fps_time = current_time
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Streaming loop error", error=str(e))
                await asyncio.sleep(0.1)  # Brief pause before retry
    
    async def _process_and_distribute_frame(self, frame_data: dict) -> None:
        """Process frame and distribute to subscribers."""
        if not self._subscribers:
            return
        
        frame = frame_data["frame"]
        timestamp = frame_data["timestamp"]
        
        # Group subscribers by encoding preferences
        encoding_groups = {}
        
        for subscriber_id, subscriber in self._subscribers.items():
            if not subscriber["active"]:
                continue
            
            key = (subscriber["encoding_format"], subscriber["quality"])
            if key not in encoding_groups:
                encoding_groups[key] = []
            encoding_groups[key].append(subscriber)
        
        # Process frame for each encoding group
        for (encoding_format, quality), subscribers in encoding_groups.items():
            try:
                # Temporarily set encoder settings
                original_format = self.encoder.format
                original_quality = self.encoder.quality
                
                self.encoder.format = encoding_format
                self.encoder.quality = quality
                
                # Encode frame
                encoded_data, encoding_metadata = await self.encoder.encode_frame(
                    frame, {"timestamp": timestamp}
                )
                
                # Restore original settings
                self.encoder.format = original_format
                self.encoder.quality = original_quality
                
                # Create stream message
                stream_message = {
                    "type": "screen_data",
                    "data": encoded_data,
                    "metadata": encoding_metadata,
                    "timestamp": timestamp,
                }
                
                # Send to all subscribers in this group
                for subscriber in subscribers:
                    try:
                        await subscriber["callback"](stream_message)
                        subscriber["frames_received"] += 1
                        subscriber["last_frame_time"] = time.perf_counter()
                    except Exception as e:
                        self.logger.error(
                            "Failed to send frame to subscriber",
                            subscriber_id=subscriber["id"],
                            error=str(e)
                        )
                        # Mark subscriber as inactive
                        subscriber["active"] = False
                
            except Exception as e:
                self.logger.error(
                    "Frame processing error",
                    encoding_format=encoding_format.value,
                    quality=quality,
                    error=str(e)
                )
    
    def get_stats(self) -> dict:
        """Get streaming statistics."""
        runtime = 0.0
        if self._stats["stream_start_time"]:
            runtime = time.perf_counter() - self._stats["stream_start_time"]
        
        # Get capture and encoding stats
        capture_stats = self.capture_manager.stats if self.capture_manager else {}
        encoding_stats = self.encoder.get_stats()
        
        return {
            **self._stats,
            "runtime_seconds": round(runtime, 2),
            "is_streaming": self.is_streaming,
            "capture_stats": capture_stats,
            "encoding_stats": encoding_stats,
            "subscribers": {
                subscriber_id: {
                    "frames_received": info["frames_received"],
                    "last_frame_time": info["last_frame_time"],
                    "active": info["active"],
                    "encoding_format": info["encoding_format"].value,
                    "quality": info["quality"],
                }
                for subscriber_id, info in self._subscribers.items()
            }
        }
    
    async def cleanup(self) -> None:
        """Cleanup all resources."""
        await self.stop_streaming()
        await self.capture_manager.cleanup()
        self.logger.info("Screen streamer cleaned up")
