"""Connection management for WebSocket clients."""

import asyncio
import time
import uuid
from typing import Dict, List, Optional, Set
from dataclasses import dataclass, field

from fastapi import WebSocket, WebSocketDisconnect
import json

from ..core.config import settings
from ..core.exceptions import WebSocketError
from ..core.logger import Logger<PERSON>ixin


@dataclass
class ClientConnection:
    """Represents a connected WebSocket client."""
    
    id: str
    websocket: WebSocket
    connected_at: float = field(default_factory=time.perf_counter)
    last_activity: float = field(default_factory=time.perf_counter)
    
    # Client preferences
    streaming: bool = False
    fps: int = 30
    effect_type: str = "matrix_classic"
    monitor: int = 0
    region: Optional[tuple] = None
    
    # Statistics
    messages_sent: int = 0
    messages_received: int = 0
    bytes_sent: int = 0
    bytes_received: int = 0
    
    def update_activity(self) -> None:
        """Update last activity timestamp."""
        self.last_activity = time.perf_counter()
    
    def get_connection_time(self) -> float:
        """Get connection duration in seconds."""
        return time.perf_counter() - self.connected_at
    
    def to_dict(self) -> dict:
        """Convert to dictionary for serialization."""
        return {
            "id": self.id,
            "connected_at": self.connected_at,
            "last_activity": self.last_activity,
            "connection_time": self.get_connection_time(),
            "streaming": self.streaming,
            "fps": self.fps,
            "effect_type": self.effect_type,
            "monitor": self.monitor,
            "region": self.region,
            "messages_sent": self.messages_sent,
            "messages_received": self.messages_received,
            "bytes_sent": self.bytes_sent,
            "bytes_received": self.bytes_received,
        }


class ConnectionManager(LoggerMixin):
    """Manages WebSocket connections and client state."""
    
    def __init__(self):
        self.connections: Dict[str, ClientConnection] = {}
        self.streaming_clients: Set[str] = set()
        self._heartbeat_task: Optional[asyncio.Task] = None
        self._cleanup_task: Optional[asyncio.Task] = None
        self._stats = {
            "total_connections": 0,
            "total_disconnections": 0,
            "messages_sent": 0,
            "messages_received": 0,
            "bytes_sent": 0,
            "bytes_received": 0,
        }
    
    async def start(self) -> None:
        """Start connection manager background tasks."""
        self._heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        self.logger.info("Connection manager started")
    
    async def stop(self) -> None:
        """Stop connection manager and disconnect all clients."""
        # Cancel background tasks
        if self._heartbeat_task:
            self._heartbeat_task.cancel()
        if self._cleanup_task:
            self._cleanup_task.cancel()
        
        # Disconnect all clients
        for client_id in list(self.connections.keys()):
            await self.disconnect_client(client_id, reason="Server shutdown")
        
        self.logger.info("Connection manager stopped")
    
    async def connect_client(self, websocket: WebSocket) -> str:
        """
        Connect a new WebSocket client.
        
        Args:
            websocket: WebSocket connection
            
        Returns:
            Client ID
        """
        # Check connection limit
        if len(self.connections) >= settings.server.max_connections:
            await websocket.close(code=1013, reason="Server at capacity")
            raise WebSocketError(
                "Maximum connections reached",
                connection_info={"max_connections": settings.server.max_connections}
            )
        
        # Accept connection
        await websocket.accept()
        
        # Create client connection
        client_id = str(uuid.uuid4())
        client = ClientConnection(
            id=client_id,
            websocket=websocket
        )
        
        self.connections[client_id] = client
        self._stats["total_connections"] += 1
        
        self.logger.info(
            "Client connected",
            client_id=client_id,
            total_connections=len(self.connections)
        )
        
        return client_id
    
    async def disconnect_client(self, client_id: str, reason: str = "Unknown") -> None:
        """
        Disconnect a client.
        
        Args:
            client_id: Client ID to disconnect
            reason: Disconnection reason
        """
        if client_id not in self.connections:
            return
        
        client = self.connections[client_id]
        
        # Stop streaming if active
        if client.streaming:
            await self.stop_streaming(client_id)
        
        # Close WebSocket
        try:
            await client.websocket.close()
        except Exception as e:
            self.logger.warning("Error closing WebSocket", error=str(e))
        
        # Remove from connections
        del self.connections[client_id]
        self.streaming_clients.discard(client_id)
        self._stats["total_disconnections"] += 1
        
        self.logger.info(
            "Client disconnected",
            client_id=client_id,
            reason=reason,
            connection_time=client.get_connection_time(),
            total_connections=len(self.connections)
        )
    
    async def send_message(self, client_id: str, message: dict) -> bool:
        """
        Send message to a specific client.
        
        Args:
            client_id: Target client ID
            message: Message to send
            
        Returns:
            True if sent successfully, False otherwise
        """
        if client_id not in self.connections:
            return False
        
        client = self.connections[client_id]
        
        try:
            message_str = json.dumps(message)
            await client.websocket.send_text(message_str)
            
            # Update statistics
            client.messages_sent += 1
            client.bytes_sent += len(message_str.encode('utf-8'))
            client.update_activity()
            
            self._stats["messages_sent"] += 1
            self._stats["bytes_sent"] += len(message_str.encode('utf-8'))
            
            return True
            
        except WebSocketDisconnect:
            await self.disconnect_client(client_id, "WebSocket disconnected")
            return False
        except Exception as e:
            self.logger.error(
                "Failed to send message",
                client_id=client_id,
                error=str(e)
            )
            return False
    
    async def broadcast_message(self, message: dict, exclude: Set[str] = None) -> int:
        """
        Broadcast message to all connected clients.
        
        Args:
            message: Message to broadcast
            exclude: Set of client IDs to exclude
            
        Returns:
            Number of clients that received the message
        """
        exclude = exclude or set()
        sent_count = 0
        
        for client_id in list(self.connections.keys()):
            if client_id not in exclude:
                if await self.send_message(client_id, message):
                    sent_count += 1
        
        return sent_count
    
    async def start_streaming(self, client_id: str) -> bool:
        """Start streaming for a client."""
        if client_id not in self.connections:
            return False
        
        client = self.connections[client_id]
        client.streaming = True
        self.streaming_clients.add(client_id)
        
        self.logger.info(
            "Streaming started",
            client_id=client_id,
            fps=client.fps,
            effect=client.effect_type
        )
        
        return True
    
    async def stop_streaming(self, client_id: str) -> bool:
        """Stop streaming for a client."""
        if client_id not in self.connections:
            return False
        
        client = self.connections[client_id]
        client.streaming = False
        self.streaming_clients.discard(client_id)
        
        self.logger.info("Streaming stopped", client_id=client_id)
        return True
    
    async def update_client_settings(self, client_id: str, settings_dict: dict) -> bool:
        """Update client settings."""
        if client_id not in self.connections:
            return False
        
        client = self.connections[client_id]
        
        # Update allowed settings
        if "fps" in settings_dict:
            client.fps = max(1, min(120, settings_dict["fps"]))
        if "effect_type" in settings_dict:
            client.effect_type = settings_dict["effect_type"]
        if "monitor" in settings_dict:
            client.monitor = max(0, settings_dict["monitor"])
        if "region" in settings_dict:
            client.region = settings_dict["region"]
        
        client.update_activity()
        
        self.logger.info(
            "Client settings updated",
            client_id=client_id,
            settings=settings_dict
        )
        
        return True
    
    def get_client(self, client_id: str) -> Optional[ClientConnection]:
        """Get client connection by ID."""
        return self.connections.get(client_id)
    
    def get_streaming_clients(self) -> List[ClientConnection]:
        """Get all clients that are currently streaming."""
        return [
            self.connections[client_id]
            for client_id in self.streaming_clients
            if client_id in self.connections
        ]
    
    def get_stats(self) -> dict:
        """Get connection manager statistics."""
        return {
            **self._stats,
            "active_connections": len(self.connections),
            "streaming_connections": len(self.streaming_clients),
            "uptime_seconds": time.perf_counter() - getattr(self, '_start_time', time.perf_counter()),
        }
    
    async def _heartbeat_loop(self) -> None:
        """Send periodic heartbeat to all clients."""
        while True:
            try:
                await asyncio.sleep(settings.server.heartbeat_interval)
                
                heartbeat_msg = {
                    "type": "heartbeat",
                    "timestamp": time.perf_counter(),
                    "server_stats": {
                        "active_connections": len(self.connections),
                        "streaming_connections": len(self.streaming_clients)
                    }
                }
                
                await self.broadcast_message(heartbeat_msg)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Heartbeat error", error=str(e))
    
    async def _cleanup_loop(self) -> None:
        """Clean up inactive connections."""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute
                
                current_time = time.perf_counter()
                inactive_clients = []
                
                for client_id, client in self.connections.items():
                    # Consider client inactive if no activity for 5 minutes
                    if current_time - client.last_activity > 300:
                        inactive_clients.append(client_id)
                
                # Disconnect inactive clients
                for client_id in inactive_clients:
                    await self.disconnect_client(client_id, "Inactive connection")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Cleanup error", error=str(e))
