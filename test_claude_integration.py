"""Test script for <PERSON> integration."""

import asyncio
import json
import subprocess
import sys
import time
from pathlib import Path


async def test_mcp_server():
    """Test MCP server functionality."""
    print("🧪 Testing Matrix Vision MCP Server")
    print("=" * 50)
    
    try:
        # Start MCP server process
        print("📡 Starting MCP server...")
        
        # Use the same command that <PERSON> would use
        process = subprocess.Popen(
            [sys.executable, "-m", "matrix_vision.mcp.mcp_server"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=0
        )
        
        # Give it a moment to start
        await asyncio.sleep(1)
        
        # Test 1: Initialize
        print("\n🔧 Test 1: Initialize MCP connection")
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "roots": [],
                    "sampling": False
                },
                "clientInfo": {
                    "name": "claude-desktop-test",
                    "version": "1.0.0"
                }
            }
        }
        
        process.stdin.write(json.dumps(init_request) + "\n")
        process.stdin.flush()
        
        # Read response
        response_line = process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            if "error" not in response:
                print("✅ Initialize successful")
                print(f"   Server: {response['result']['server_info']['name']}")
            else:
                print(f"❌ Initialize failed: {response['error']}")
                return False
        
        # Send initialized notification
        initialized_notification = {
            "jsonrpc": "2.0",
            "method": "notifications/initialized"
        }
        process.stdin.write(json.dumps(initialized_notification) + "\n")
        process.stdin.flush()
        
        # Test 2: List tools
        print("\n🛠️  Test 2: List available tools")
        tools_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list"
        }
        
        process.stdin.write(json.dumps(tools_request) + "\n")
        process.stdin.flush()
        
        response_line = process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            if "error" not in response:
                tools = response['result']['tools']
                print(f"✅ Found {len(tools)} tools:")
                for tool in tools:
                    print(f"   - {tool['name']}: {tool['description']}")
            else:
                print(f"❌ List tools failed: {response['error']}")
        
        # Test 3: Test screen capture tool
        print("\n📸 Test 3: Test screen capture")
        capture_request = {
            "jsonrpc": "2.0",
            "id": 3,
            "method": "tools/call",
            "params": {
                "name": "capture_screen",
                "arguments": {
                    "monitor": 0,
                    "format": "base64_jpeg",
                    "quality": 75
                }
            }
        }
        
        process.stdin.write(json.dumps(capture_request) + "\n")
        process.stdin.flush()
        
        response_line = process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            if "error" not in response:
                content = response['result']['content']
                print("✅ Screen capture successful")
                
                for item in content:
                    if item['type'] == 'text':
                        print(f"   Text: {item['text']}")
                    elif item['type'] == 'image':
                        print(f"   Image: {len(item['data'])} characters (base64)")
                        
                        # Save test image
                        import base64
                        output_dir = Path("test_output")
                        output_dir.mkdir(exist_ok=True)
                        
                        with open(output_dir / "test_capture.jpg", "wb") as f:
                            f.write(base64.b64decode(item['data']))
                        print(f"   💾 Saved to: {output_dir / 'test_capture.jpg'}")
            else:
                print(f"❌ Screen capture failed: {response['error']}")
        
        # Test 4: Test system info
        print("\n💻 Test 4: Test system info")
        system_request = {
            "jsonrpc": "2.0",
            "id": 4,
            "method": "tools/call",
            "params": {
                "name": "get_system_info",
                "arguments": {
                    "include_monitors": True,
                    "include_performance": False
                }
            }
        }
        
        process.stdin.write(json.dumps(system_request) + "\n")
        process.stdin.flush()
        
        response_line = process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            if "error" not in response:
                print("✅ System info retrieved")
                content = response['result']['content']
                for item in content:
                    if item['type'] == 'text':
                        # Parse the JSON content
                        try:
                            system_data = json.loads(item['text'])
                            if 'system_info' in system_data:
                                info = system_data['system_info']
                                print(f"   Platform: {info.get('platform', 'Unknown')}")
                                print(f"   Python: {info.get('python_version', 'Unknown')}")
                                print(f"   Matrix Vision: {info.get('matrix_vision_version', 'Unknown')}")
                            
                            if 'monitors' in system_data and system_data['monitors']:
                                print(f"   Monitors: {len(system_data['monitors'])}")
                                for i, monitor in enumerate(system_data['monitors']):
                                    print(f"     Monitor {i}: {monitor.get('width', '?')}x{monitor.get('height', '?')}")
                        except:
                            print(f"   Raw response: {item['text'][:200]}...")
            else:
                print(f"❌ System info failed: {response['error']}")
        
        print("\n🎉 All tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
    
    finally:
        # Clean up
        if 'process' in locals():
            process.terminate()
            process.wait()


def create_claude_config():
    """Create Claude Desktop configuration."""
    print("\n📋 Creating Claude Desktop Configuration")
    print("=" * 50)
    
    # Determine the correct path for Claude Desktop config
    import platform
    
    if platform.system() == "Windows":
        config_dir = Path.home() / "AppData" / "Roaming" / "Claude"
    elif platform.system() == "Darwin":  # macOS
        config_dir = Path.home() / "Library" / "Application Support" / "Claude"
    else:  # Linux
        config_dir = Path.home() / ".config" / "claude"
    
    config_file = config_dir / "claude_desktop_config.json"
    
    print(f"📁 Config directory: {config_dir}")
    print(f"📄 Config file: {config_file}")
    
    # Create directory if it doesn't exist
    config_dir.mkdir(parents=True, exist_ok=True)
    
    # Get the current Python executable path
    python_path = sys.executable
    
    # Create configuration
    config = {
        "mcpServers": {
            "matrix-vision": {
                "command": python_path,
                "args": ["-m", "matrix_vision.mcp.mcp_server"],
                "env": {
                    "MATRIX_VISION_ENVIRONMENT": "production",
                    "MATRIX_VISION_LOGGING__LEVEL": "INFO",
                    "MATRIX_VISION_CAPTURE__FPS": "5",
                    "MATRIX_VISION_CAPTURE__QUALITY": "75"
                }
            }
        }
    }
    
    # Write configuration
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print("✅ Claude Desktop configuration created!")
    print(f"📝 Configuration written to: {config_file}")
    
    # Show the configuration
    print("\n📋 Configuration content:")
    print(json.dumps(config, indent=2))
    
    return config_file


def show_usage_instructions():
    """Show usage instructions for Claude Desktop."""
    print("\n📚 Claude Desktop Usage Instructions")
    print("=" * 50)
    
    print("""
🚀 How to use Matrix Vision with Claude Desktop:

1. 📥 Make sure Claude Desktop is installed and closed

2. 🔧 The configuration has been automatically created at the correct location

3. 🔄 Restart Claude Desktop to load the new MCP server

4. 💬 In Claude Desktop, you can now use these commands:

   📸 "Capture my current screen"
   📸 "Take a screenshot and analyze it"
   📸 "What's currently on my screen?"
   📸 "Capture a specific region of my screen"
   
   💻 "Get system information"
   💻 "List available monitors"
   
   🎥 "Start vision streaming for AI analysis"
   🎥 "Stop the vision stream"

5. 🎯 Example conversations:
   
   You: "Can you capture my screen and tell me what applications are open?"
   Claude: [Uses capture_screen tool and analyzes the image]
   
   You: "Start monitoring my screen for changes"
   Claude: [Uses start_vision_stream tool]

6. 🔍 Troubleshooting:
   - If tools don't appear, restart Claude Desktop
   - Check that Matrix Vision is properly installed: `matrix-vision --version`
   - Test MCP server manually: `matrix-vision mcp`

🎉 You're all set! Claude Desktop can now see your screen!
""")


async def main():
    """Main test function."""
    print("🎯 Matrix Vision - Claude Desktop Integration Test")
    print("=" * 60)
    
    # Test 1: Test MCP server functionality
    success = await test_mcp_server()
    
    if success:
        # Test 2: Create Claude Desktop configuration
        config_file = create_claude_config()
        
        # Test 3: Show usage instructions
        show_usage_instructions()
        
        print("\n" + "=" * 60)
        print("🎉 Integration test completed successfully!")
        print("🚀 Claude Desktop is ready to use Matrix Vision!")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ Integration test failed!")
        print("🔧 Please check the error messages above and fix any issues.")
        print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
