"""Main screen capture interface and manager."""

import asyncio
import base64
import io
import time
from typing import <PERSON><PERSON>, <PERSON><PERSON>, Union

import numpy as np
from PIL import Image

from ..core.config import settings
from ..core.exceptions import ScreenCaptureError
from ..core.logger import <PERSON><PERSON><PERSON><PERSON><PERSON>, PerformanceLogger
from .capture_backends import CaptureBackend, get_best_backend
from .frame_buffer import <PERSON><PERSON><PERSON>uff<PERSON>, FrameProcessor


class ScreenCapture(LoggerMixin):
    """High-level screen capture interface."""
    
    def __init__(self, backend: Optional[CaptureBackend] = None):
        self.backend = backend or get_best_backend()
        self.frame_processor = FrameProcessor(
            target_fps=settings.capture.fps,
            quality=settings.capture.quality,
            max_width=settings.capture.max_width,
            max_height=settings.capture.max_height
        )
        self._perf_logger = PerformanceLogger(self.logger)
        self.is_initialized = False
    
    async def initialize(self) -> None:
        """Initialize the screen capture system."""
        if self.is_initialized:
            return
        
        self._perf_logger.start_timer("capture_init")
        
        try:
            await self.backend.initialize()
            self.is_initialized = True
            
            # Log available monitors
            monitors = await self.backend.get_monitors()
            self.logger.info(
                "Screen capture initialized",
                backend=self.backend.__class__.__name__,
                monitors_count=len(monitors),
                target_fps=settings.capture.fps
            )
            
        except Exception as e:
            raise ScreenCaptureError(f"Failed to initialize screen capture: {e}") from e
        finally:
            self._perf_logger.end_timer("capture_init")
    
    async def capture_frame(
        self,
        monitor: int = None,
        region: Optional[Tuple[int, int, int, int]] = None,
        format_output: str = "RGB"
    ) -> Tuple[np.ndarray, dict]:
        """
        Capture a single frame.
        
        Args:
            monitor: Monitor index (defaults to config)
            region: Capture region (x, y, width, height)
            format_output: Output format ("RGB", "BGR", "GRAY")
            
        Returns:
            Tuple of (frame_array, metadata)
        """
        if not self.is_initialized:
            await self.initialize()
        
        monitor = monitor if monitor is not None else settings.capture.monitor
        region = region or settings.capture.region
        
        self._perf_logger.start_timer("capture_frame")
        
        try:
            # Capture raw frame
            raw_frame = await self.backend.capture_screen(monitor, region)
            
            # Process frame
            processed_frame, metadata = await self.frame_processor.process_frame(
                raw_frame, resize=True, format_output=format_output
            )
            
            # Add capture metadata
            metadata.update({
                "monitor": monitor,
                "region": region,
                "backend": self.backend.__class__.__name__,
                "capture_time": time.perf_counter()
            })
            
            return processed_frame, metadata
            
        except Exception as e:
            raise ScreenCaptureError(f"Frame capture failed: {e}") from e
        finally:
            self._perf_logger.end_timer("capture_frame")
    
    async def capture_frame_base64(
        self,
        monitor: int = None,
        region: Optional[Tuple[int, int, int, int]] = None,
        format_output: str = "JPEG"
    ) -> Tuple[str, dict]:
        """
        Capture frame and encode as base64.
        
        Args:
            monitor: Monitor index
            region: Capture region
            format_output: Image format ("JPEG", "PNG")
            
        Returns:
            Tuple of (base64_string, metadata)
        """
        frame, metadata = await self.capture_frame(monitor, region, "RGB")
        
        # Convert to PIL Image
        pil_image = Image.fromarray(frame)
        
        # Encode to base64
        buffer = io.BytesIO()
        pil_image.save(
            buffer,
            format=format_output,
            quality=settings.capture.quality if format_output == "JPEG" else None
        )
        
        base64_string = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        metadata.update({
            "encoding": "base64",
            "image_format": format_output,
            "size_bytes": len(base64_string)
        })
        
        return base64_string, metadata
    
    async def get_monitors(self) -> list:
        """Get available monitors."""
        if not self.is_initialized:
            await self.initialize()
        return await self.backend.get_monitors()
    
    async def cleanup(self) -> None:
        """Cleanup resources."""
        if self.backend:
            await self.backend.cleanup()
        self.is_initialized = False
        self.logger.info("Screen capture cleaned up")


class CaptureManager(LoggerMixin):
    """Manages continuous screen capture with buffering."""
    
    def __init__(
        self,
        capture: Optional[ScreenCapture] = None,
        buffer_size: int = 10
    ):
        self.capture = capture or ScreenCapture()
        self.frame_buffer = FrameBuffer(max_size=buffer_size)
        self.is_running = False
        self._capture_task = None
        self._stats = {
            "frames_captured": 0,
            "frames_dropped": 0,
            "start_time": None,
            "last_fps": 0.0
        }
    
    async def start_capture(
        self,
        monitor: int = None,
        region: Optional[Tuple[int, int, int, int]] = None
    ) -> None:
        """Start continuous capture."""
        if self.is_running:
            self.logger.warning("Capture already running")
            return
        
        await self.capture.initialize()
        
        self.is_running = True
        self._stats["start_time"] = time.perf_counter()
        
        # Start capture task
        self._capture_task = asyncio.create_task(
            self._capture_loop(monitor, region)
        )
        
        self.logger.info("Continuous capture started")
    
    async def stop_capture(self) -> None:
        """Stop continuous capture."""
        if not self.is_running:
            return
        
        self.is_running = False
        
        if self._capture_task:
            self._capture_task.cancel()
            try:
                await self._capture_task
            except asyncio.CancelledError:
                pass
        
        await self.frame_buffer.clear()
        self.logger.info("Continuous capture stopped")
    
    async def _capture_loop(
        self,
        monitor: int = None,
        region: Optional[Tuple[int, int, int, int]] = None
    ) -> None:
        """Main capture loop."""
        last_fps_time = time.perf_counter()
        fps_counter = 0
        
        while self.is_running:
            try:
                # Capture frame
                frame, metadata = await self.capture.capture_frame(
                    monitor, region, "RGB"
                )
                
                # Add to buffer
                await self.frame_buffer.put_frame(frame, metadata["capture_time"])
                
                self._stats["frames_captured"] += 1
                fps_counter += 1
                
                # Calculate FPS every second
                current_time = time.perf_counter()
                if current_time - last_fps_time >= 1.0:
                    self._stats["last_fps"] = fps_counter / (current_time - last_fps_time)
                    fps_counter = 0
                    last_fps_time = current_time
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Capture loop error", error=str(e))
                await asyncio.sleep(0.1)  # Brief pause before retry
    
    async def get_latest_frame(self) -> Optional[dict]:
        """Get the latest captured frame."""
        return await self.frame_buffer.get_latest_frame()
    
    async def get_frame(self, timeout: float = 1.0) -> Optional[dict]:
        """Get next available frame."""
        return await self.frame_buffer.get_frame(timeout)
    
    @property
    def stats(self) -> dict:
        """Get capture statistics."""
        runtime = 0.0
        if self._stats["start_time"]:
            runtime = time.perf_counter() - self._stats["start_time"]
        
        buffer_stats = self.frame_buffer.stats
        
        return {
            **self._stats,
            "runtime_seconds": round(runtime, 2),
            "avg_fps": round(self._stats["frames_captured"] / max(runtime, 1), 2),
            "buffer_stats": buffer_stats,
            "is_running": self.is_running
        }
    
    async def cleanup(self) -> None:
        """Cleanup all resources."""
        await self.stop_capture()
        await self.capture.cleanup()
        self.logger.info("Capture manager cleaned up")
