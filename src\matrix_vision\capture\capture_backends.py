"""Screen capture backends for different platforms and performance requirements."""

import asyncio
import time
from abc import ABC, abstractmethod
from typing import Op<PERSON>, Tuple, Union

import numpy as np
from PIL import Image

from ..core.config import settings
from ..core.exceptions import ScreenCaptureError
from ..core.logger import LoggerMixin


class CaptureBackend(ABC, LoggerMixin):
    """Abstract base class for screen capture backends."""
    
    def __init__(self):
        self.is_initialized = False
        self._last_capture_time = 0.0
        self._capture_count = 0
        
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the capture backend."""
        pass
    
    @abstractmethod
    async def capture_screen(
        self,
        monitor: int = 0,
        region: Optional[Tuple[int, int, int, int]] = None
    ) -> np.ndarray:
        """
        Capture screen as numpy array.
        
        Args:
            monitor: Monitor index to capture
            region: Region to capture (x, y, width, height)
            
        Returns:
            Captured screen as numpy array (H, W, C)
        """
        pass
    
    @abstractmethod
    async def get_monitors(self) -> list:
        """Get list of available monitors."""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup resources."""
        pass
    
    def get_fps(self) -> float:
        """Calculate current FPS based on capture timing."""
        current_time = time.perf_counter()
        if self._last_capture_time > 0:
            fps = 1.0 / (current_time - self._last_capture_time)
        else:
            fps = 0.0
        self._last_capture_time = current_time
        self._capture_count += 1
        return fps


class MSSBackend(CaptureBackend):
    """MSS (Multiple Screen Shot) backend - fastest cross-platform option."""
    
    def __init__(self):
        super().__init__()
        self._mss = None
        self._monitors = []
    
    async def initialize(self) -> None:
        """Initialize MSS backend."""
        try:
            import mss
            
            # Run in executor to avoid blocking
            loop = asyncio.get_event_loop()
            self._mss = await loop.run_in_executor(None, mss.mss)
            self._monitors = await loop.run_in_executor(None, lambda: self._mss.monitors)
            
            self.is_initialized = True
            self.logger.info(
                "MSS backend initialized",
                monitors_count=len(self._monitors) - 1,  # Exclude "All in One" monitor
                monitors=self._monitors[1:]  # Skip first monitor (all screens)
            )
            
        except ImportError as e:
            raise ScreenCaptureError(
                "MSS library not available. Install with: pip install mss"
            ) from e
        except Exception as e:
            raise ScreenCaptureError(f"Failed to initialize MSS backend: {e}") from e
    
    async def capture_screen(
        self,
        monitor: int = 0,
        region: Optional[Tuple[int, int, int, int]] = None
    ) -> np.ndarray:
        """Capture screen using MSS."""
        if not self.is_initialized:
            await self.initialize()
        
        try:
            # Determine capture area
            if region:
                capture_area = {
                    "left": region[0],
                    "top": region[1], 
                    "width": region[2],
                    "height": region[3]
                }
            else:
                # Use specific monitor (1-indexed in MSS)
                monitor_idx = monitor + 1
                if monitor_idx >= len(self._monitors):
                    raise ScreenCaptureError(
                        f"Monitor {monitor} not available. Available monitors: {len(self._monitors) - 1}",
                        monitor=monitor
                    )
                capture_area = self._monitors[monitor_idx]
            
            # Capture screen in executor to avoid blocking
            loop = asyncio.get_event_loop()
            screenshot = await loop.run_in_executor(
                None,
                self._mss.grab,
                capture_area
            )
            
            # Convert to numpy array
            img_array = np.frombuffer(screenshot.rgb, dtype=np.uint8)
            img_array = img_array.reshape((screenshot.height, screenshot.width, 3))
            
            return img_array
            
        except Exception as e:
            raise ScreenCaptureError(
                f"Failed to capture screen: {e}",
                monitor=monitor,
                region=region
            ) from e
    
    async def get_monitors(self) -> list:
        """Get list of available monitors."""
        if not self.is_initialized:
            await self.initialize()
        
        # Return monitors excluding the "All in One" monitor
        return self._monitors[1:]
    
    async def cleanup(self) -> None:
        """Cleanup MSS resources."""
        if self._mss:
            try:
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(None, self._mss.close)
            except Exception as e:
                self.logger.warning("Error during MSS cleanup", error=str(e))
            finally:
                self._mss = None
                self.is_initialized = False


class PILBackend(CaptureBackend):
    """PIL/Pillow backend - fallback option."""
    
    def __init__(self):
        super().__init__()
        self._screen_size = None
    
    async def initialize(self) -> None:
        """Initialize PIL backend."""
        try:
            from PIL import ImageGrab
            
            # Test capture to verify functionality
            loop = asyncio.get_event_loop()
            test_img = await loop.run_in_executor(None, ImageGrab.grab)
            self._screen_size = test_img.size
            
            self.is_initialized = True
            self.logger.info(
                "PIL backend initialized",
                screen_size=self._screen_size
            )
            
        except ImportError as e:
            raise ScreenCaptureError(
                "PIL library not available. Install with: pip install Pillow"
            ) from e
        except Exception as e:
            raise ScreenCaptureError(f"Failed to initialize PIL backend: {e}") from e
    
    async def capture_screen(
        self,
        monitor: int = 0,
        region: Optional[Tuple[int, int, int, int]] = None
    ) -> np.ndarray:
        """Capture screen using PIL."""
        if not self.is_initialized:
            await self.initialize()
        
        try:
            from PIL import ImageGrab
            
            # PIL doesn't support multiple monitors directly
            if monitor > 0:
                self.logger.warning(
                    "PIL backend doesn't support multiple monitors, using primary monitor"
                )
            
            # Capture screen in executor
            loop = asyncio.get_event_loop()
            if region:
                bbox = (region[0], region[1], region[0] + region[2], region[1] + region[3])
                screenshot = await loop.run_in_executor(None, ImageGrab.grab, bbox)
            else:
                screenshot = await loop.run_in_executor(None, ImageGrab.grab)
            
            # Convert to numpy array
            img_array = np.array(screenshot)
            
            # Ensure RGB format
            if img_array.shape[2] == 4:  # RGBA
                img_array = img_array[:, :, :3]  # Drop alpha channel
            
            return img_array
            
        except Exception as e:
            raise ScreenCaptureError(
                f"Failed to capture screen: {e}",
                monitor=monitor,
                region=region
            ) from e
    
    async def get_monitors(self) -> list:
        """Get list of available monitors (PIL only supports primary)."""
        if not self.is_initialized:
            await self.initialize()
        
        return [{"left": 0, "top": 0, "width": self._screen_size[0], "height": self._screen_size[1]}]
    
    async def cleanup(self) -> None:
        """Cleanup PIL resources."""
        self.is_initialized = False


def get_best_backend() -> CaptureBackend:
    """
    Get the best available capture backend for the current platform.
    
    Returns:
        Best available capture backend
    """
    # Try MSS first (fastest)
    try:
        import mss
        return MSSBackend()
    except ImportError:
        pass
    
    # Fallback to PIL
    try:
        from PIL import ImageGrab
        return PILBackend()
    except ImportError:
        pass
    
    raise ScreenCaptureError(
        "No screen capture backend available. Install mss or Pillow: "
        "pip install mss pillow"
    )
