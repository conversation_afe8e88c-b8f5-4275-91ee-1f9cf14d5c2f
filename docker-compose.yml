version: '3.8'

services:
  matrix-vision:
    build: .
    container_name: matrix-vision-server
    ports:
      - "8765:8765"
    environment:
      - MATRIX_VISION_ENVIRONMENT=production
      - MATRIX_VISION_SERVER__HOST=0.0.0.0
      - MATRIX_VISION_SERVER__PORT=8765
      - MATRIX_VISION_SERVER__MAX_CONNECTIONS=100
      - MATRIX_VISION_CAPTURE__FPS=30
      - MATRIX_VISION_CAPTURE__QUALITY=85
      - MATRIX_VISION_LOGGING__LEVEL=INFO
    volumes:
      - matrix_vision_data:/app/data
      - matrix_vision_logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8765/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: matrix-vision-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Optional: Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: matrix-vision-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - matrix-vision
    restart: unless-stopped

volumes:
  matrix_vision_data:
  matrix_vision_logs:
  redis_data:
