# Matrix Vision Docker Image
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV MATRIX_VISION_ENVIRONMENT=production

# Install system dependencies
RUN apt-get update && apt-get install -y \
    # For screen capture
    libx11-dev \
    libxrandr-dev \
    libxinerama-dev \
    libxcursor-dev \
    libxi-dev \
    # For image processing
    libjpeg-dev \
    libpng-dev \
    libwebp-dev \
    # Build tools
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN useradd --create-home --shell /bin/bash app

# Set work directory
WORKDIR /app

# Copy requirements first for better caching
COPY pyproject.toml ./
COPY README.md ./

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir .

# Copy application code
COPY src/ ./src/
COPY examples/ ./examples/

# Change ownership to app user
R<PERSON> chown -R app:app /app

# Switch to app user
USER app

# Create data directory
RUN mkdir -p /app/data

# Expose ports
EXPOSE 8765

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8765/health')" || exit 1

# Default command
CMD ["matrix-vision", "server", "--host", "0.0.0.0", "--port", "8765"]
