"""Basic usage examples for Matrix Vision."""

import asyncio
import base64
from pathlib import Path

from matrix_vision import ScreenCapture, ScreenStreamer, DataEncoder
from matrix_vision.streaming.data_encoder import EncodingFormat


async def example_single_capture():
    """Example: Capture a single screenshot."""
    print("=== Single Screen Capture ===")
    
    capture = ScreenCapture()
    
    try:
        await capture.initialize()
        
        # Capture current screen
        frame, metadata = await capture.capture_frame()
        print(f"Captured frame: {frame.shape} ({frame.dtype})")
        print(f"Metadata: {metadata}")
        
        # Get as base64
        base64_data, encoding_metadata = await capture.capture_frame_base64()
        print(f"Base64 data length: {len(base64_data)} characters")
        print(f"Encoding metadata: {encoding_metadata}")
        
        # Save to file
        output_dir = Path("output")
        output_dir.mkdir(exist_ok=True)
        
        with open(output_dir / "screenshot.jpg", "wb") as f:
            f.write(base64.b64decode(base64_data))
        
        print("Screenshot saved to output/screenshot.jpg")
        
    finally:
        await capture.cleanup()


async def example_streaming():
    """Example: Real-time screen streaming."""
    print("\n=== Real-time Screen Streaming ===")
    
    streamer = ScreenStreamer()
    frame_count = 0
    
    async def frame_callback(frame_data):
        nonlocal frame_count
        frame_count += 1
        
        print(f"Frame {frame_count}:")
        print(f"  - Data size: {len(frame_data['data'])} bytes")
        print(f"  - Timestamp: {frame_data['timestamp']}")
        print(f"  - Metadata: {frame_data['metadata']['encoding_format']}")
    
    try:
        # Start streaming at 5 FPS
        await streamer.start_streaming(fps=5)
        
        # Subscribe to stream
        await streamer.subscribe("example-session", frame_callback)
        
        print("Streaming started. Will capture 10 frames...")
        
        # Stream for 10 frames
        while frame_count < 10:
            await asyncio.sleep(0.5)
        
        print(f"Captured {frame_count} frames")
        
        # Get statistics
        stats = streamer.get_stats()
        print(f"Streaming stats: {stats}")
        
    finally:
        await streamer.cleanup()


async def example_different_formats():
    """Example: Different encoding formats."""
    print("\n=== Different Encoding Formats ===")
    
    capture = ScreenCapture()
    encoder = DataEncoder()
    
    try:
        await capture.initialize()
        
        # Capture a frame
        frame, _ = await capture.capture_frame()
        print(f"Original frame size: {frame.nbytes} bytes")
        
        # Test different formats
        formats = [
            (EncodingFormat.BASE64_JPEG, 85),
            (EncodingFormat.BASE64_PNG, None),
            (EncodingFormat.BASE64_WEBP, 80),
        ]
        
        for format_type, quality in formats:
            encoder.format = format_type
            if quality:
                encoder.quality = quality
            
            encoded_data, metadata = await encoder.encode_frame(frame)
            
            print(f"\n{format_type.value}:")
            print(f"  - Encoded size: {len(encoded_data)} characters")
            print(f"  - Compression ratio: {metadata['compression_ratio']}")
            print(f"  - Encoding time: {metadata['encoding_time_ms']}ms")
    
    finally:
        await capture.cleanup()


async def example_monitor_selection():
    """Example: Monitor selection and region capture."""
    print("\n=== Monitor Selection ===")
    
    capture = ScreenCapture()
    
    try:
        await capture.initialize()
        
        # List available monitors
        monitors = await capture.get_monitors()
        print(f"Available monitors: {len(monitors)}")
        
        for i, monitor in enumerate(monitors):
            print(f"  Monitor {i}: {monitor}")
        
        if len(monitors) > 0:
            # Capture from first monitor
            frame, metadata = await capture.capture_frame(monitor=0)
            print(f"Captured from monitor 0: {frame.shape}")
            
            # Capture a region (top-left 800x600)
            region = (0, 0, 800, 600)
            frame, metadata = await capture.capture_frame(region=region)
            print(f"Captured region {region}: {frame.shape}")
    
    finally:
        await capture.cleanup()


async def example_ai_optimized_streaming():
    """Example: AI-optimized streaming for different clients."""
    print("\n=== AI-Optimized Streaming ===")
    
    encoder = DataEncoder()
    capture = ScreenCapture()
    
    try:
        await capture.initialize()
        frame, _ = await capture.capture_frame()
        
        # Optimize for different AI clients
        ai_clients = ["claude", "gpt", "gemini"]
        
        for client in ai_clients:
            await encoder.optimize_for_ai_client(client)
            
            encoded_data, metadata = await encoder.encode_frame(frame)
            
            print(f"\nOptimized for {client}:")
            print(f"  - Format: {encoder.format.value}")
            print(f"  - Quality: {encoder.quality}")
            print(f"  - Size: {len(encoded_data)} characters")
            print(f"  - Compression: {metadata['compression_ratio']:.2f}x")
    
    finally:
        await capture.cleanup()


async def main():
    """Run all examples."""
    print("Matrix Vision - Basic Usage Examples")
    print("=" * 50)
    
    try:
        await example_single_capture()
        await example_streaming()
        await example_different_formats()
        await example_monitor_selection()
        await example_ai_optimized_streaming()
        
        print("\n" + "=" * 50)
        print("All examples completed successfully!")
        
    except KeyboardInterrupt:
        print("\nExamples interrupted by user")
    except Exception as e:
        print(f"\nError running examples: {e}")


if __name__ == "__main__":
    asyncio.run(main())
