"""Structured logging setup for Matrix Vision."""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional

import structlog
from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>

from .config import settings


def setup_logging(
    level: Optional[str] = None,
    log_file: Optional[Path] = None,
    structured: bool = True,
) -> None:
    """
    Setup logging configuration.
    
    Args:
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file
        structured: Use structured logging
    """
    log_level = level or settings.logging.level
    log_file = log_file or settings.get_log_file_path()
    
    # Configure structlog
    if structured:
        structlog.configure(
            processors=[
                structlog.contextvars.merge_contextvars,
                structlog.processors.add_log_level,
                structlog.processors.StackInfoRenderer(),
                structlog.dev.set_exc_info,
                structlog.processors.TimeStamper(fmt="ISO"),
                structlog.dev.ConsoleRenderer() if settings.is_development else structlog.processors.JSONRenderer(),
            ],
            wrapper_class=structlog.make_filtering_bound_logger(getattr(logging, log_level)),
            logger_factory=structlog.WriteLoggerFactory(),
            cache_logger_on_first_use=True,
        )
    
    # Setup standard logging
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler with Rich formatting
    console = Console(stderr=True)
    console_handler = RichHandler(
        console=console,
        show_time=True,
        show_path=settings.is_development,
        markup=True,
        rich_tracebacks=True,
    )
    console_handler.setLevel(getattr(logging, log_level))
    
    # Console formatter
    console_formatter = logging.Formatter(
        fmt="%(message)s",
        datefmt="[%X]",
    )
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # File handler (if specified)
    if log_file:
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            filename=log_file,
            maxBytes=settings.logging.max_file_size,
            backupCount=settings.logging.backup_count,
            encoding="utf-8",
        )
        file_handler.setLevel(getattr(logging, log_level))
        
        # File formatter
        file_formatter = logging.Formatter(
            fmt=settings.logging.format,
            datefmt="%Y-%m-%d %H:%M:%S",
        )
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
    
    # Suppress noisy loggers in production
    if settings.is_production:
        logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
        logging.getLogger("websockets").setLevel(logging.WARNING)
        logging.getLogger("asyncio").setLevel(logging.WARNING)


def get_logger(name: str) -> structlog.BoundLogger:
    """
    Get a structured logger instance.
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Configured structlog logger
    """
    return structlog.get_logger(name)


class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""
    
    @property
    def logger(self) -> structlog.BoundLogger:
        """Get logger for this class."""
        return get_logger(self.__class__.__module__ + "." + self.__class__.__name__)


# Performance logging utilities
class PerformanceLogger:
    """Utility for performance logging."""
    
    def __init__(self, logger: structlog.BoundLogger):
        self.logger = logger
        self._timers = {}
    
    def start_timer(self, name: str) -> None:
        """Start a performance timer."""
        import time
        self._timers[name] = time.perf_counter()
        self.logger.debug("Timer started", timer=name)
    
    def end_timer(self, name: str) -> float:
        """End a performance timer and log the duration."""
        import time
        if name not in self._timers:
            self.logger.warning("Timer not found", timer=name)
            return 0.0
        
        duration = time.perf_counter() - self._timers[name]
        del self._timers[name]
        
        self.logger.info(
            "Timer completed",
            timer=name,
            duration_ms=round(duration * 1000, 2)
        )
        return duration
    
    def log_memory_usage(self) -> None:
        """Log current memory usage."""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            
            self.logger.info(
                "Memory usage",
                rss_mb=round(memory_info.rss / 1024 / 1024, 2),
                vms_mb=round(memory_info.vms / 1024 / 1024, 2),
                percent=round(process.memory_percent(), 2)
            )
        except ImportError:
            self.logger.warning("psutil not available for memory monitoring")


# Initialize logging on import
setup_logging()
