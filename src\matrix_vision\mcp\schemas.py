"""MCP protocol schemas and data models."""

from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field
from enum import Enum


class MCPMessageType(str, Enum):
    """MCP message types."""
    REQUEST = "request"
    RESPONSE = "response"
    NOTIFICATION = "notification"


class MCPErrorCode(int, Enum):
    """MCP error codes."""
    PARSE_ERROR = -32700
    INVALID_REQUEST = -32600
    METHOD_NOT_FOUND = -32601
    INVALID_PARAMS = -32602
    INTERNAL_ERROR = -32603
    
    # Custom error codes
    SCREEN_CAPTURE_ERROR = -32000
    STREAMING_ERROR = -32001
    PERMISSION_DENIED = -32002


class MCPError(BaseModel):
    """MCP error object."""
    code: int
    message: str
    data: Optional[Dict[str, Any]] = None


class MCPRequest(BaseModel):
    """MCP request message."""
    jsonrpc: str = "2.0"
    method: str
    params: Optional[Dict[str, Any]] = None
    id: Optional[Union[str, int]] = None


class MCPResponse(BaseModel):
    """MCP response message."""
    jsonrpc: str = "2.0"
    id: Optional[Union[str, int]] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[MCPError] = None


class MCPNotification(BaseModel):
    """MCP notification message."""
    jsonrpc: str = "2.0"
    method: str
    params: Optional[Dict[str, Any]] = None


class ToolParameter(BaseModel):
    """Tool parameter definition."""
    name: str
    type: str
    description: str
    required: bool = True
    default: Optional[Any] = None
    enum: Optional[List[str]] = None


class ToolDefinition(BaseModel):
    """Tool definition for MCP."""
    name: str
    description: str
    parameters: List[ToolParameter]
    returns: Optional[str] = None


class ToolCall(BaseModel):
    """Tool call request."""
    tool: str
    parameters: Dict[str, Any] = Field(default_factory=dict)
    call_id: Optional[str] = None


class ToolResult(BaseModel):
    """Tool execution result."""
    call_id: Optional[str] = None
    success: bool
    result: Optional[Any] = None
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class ServerCapabilities(BaseModel):
    """Server capabilities declaration."""
    tools: bool = True
    resources: bool = False
    prompts: bool = False
    logging: bool = True


class ClientCapabilities(BaseModel):
    """Client capabilities declaration."""
    roots: Optional[List[str]] = None
    sampling: Optional[bool] = None


class InitializeRequest(BaseModel):
    """Initialize request parameters."""
    protocol_version: str = "2024-11-05"
    capabilities: ClientCapabilities
    client_info: Dict[str, str]


class InitializeResponse(BaseModel):
    """Initialize response."""
    protocol_version: str = "2024-11-05"
    capabilities: ServerCapabilities
    server_info: Dict[str, str]


# Screen capture specific schemas
class ScreenCaptureParams(BaseModel):
    """Parameters for screen capture operations."""
    monitor: Optional[int] = 0
    region: Optional[List[int]] = None  # [x, y, width, height]
    format: Optional[str] = "base64_jpeg"
    quality: Optional[int] = 85


class StreamingParams(BaseModel):
    """Parameters for streaming operations."""
    session_id: str
    fps: Optional[int] = 10
    encoding_format: Optional[str] = "base64_jpeg"
    quality: Optional[int] = 85
    monitor: Optional[int] = 0
    region: Optional[List[int]] = None


class SystemInfoParams(BaseModel):
    """Parameters for system info operations."""
    include_monitors: Optional[bool] = True
    include_performance: Optional[bool] = False


# Tool result schemas
class ScreenCaptureResult(BaseModel):
    """Screen capture result."""
    success: bool
    data: Optional[str] = None  # Base64 encoded image
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class StreamingResult(BaseModel):
    """Streaming operation result."""
    success: bool
    session_id: Optional[str] = None
    status: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class SystemInfoResult(BaseModel):
    """System info result."""
    success: bool
    system_info: Optional[Dict[str, Any]] = None
    monitors: Optional[List[Dict[str, Any]]] = None
    performance: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
