#!/bin/bash
# Matrix Vision Installation Script

set -e

echo "🚀 Matrix Vision Installation Script"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python is installed
check_python() {
    print_status "Checking Python installation..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        print_success "Python $PYTHON_VERSION found"
        
        # Check if version is >= 3.9
        if python3 -c "import sys; exit(0 if sys.version_info >= (3, 9) else 1)"; then
            print_success "Python version is compatible"
        else
            print_error "Python 3.9 or higher is required"
            exit 1
        fi
    else
        print_error "Python 3 is not installed"
        print_status "Please install Python 3.9 or higher from https://python.org"
        exit 1
    fi
}

# Install system dependencies
install_system_deps() {
    print_status "Installing system dependencies..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command -v apt-get &> /dev/null; then
            print_status "Detected Debian/Ubuntu system"
            sudo apt-get update
            sudo apt-get install -y \
                python3-pip \
                python3-venv \
                libx11-dev \
                libxrandr-dev \
                libxinerama-dev \
                libxcursor-dev \
                libxi-dev \
                libjpeg-dev \
                libpng-dev \
                libwebp-dev \
                gcc \
                g++
        elif command -v yum &> /dev/null; then
            print_status "Detected RHEL/CentOS system"
            sudo yum install -y \
                python3-pip \
                python3-devel \
                libX11-devel \
                libXrandr-devel \
                libXinerama-devel \
                libXcursor-devel \
                libXi-devel \
                libjpeg-devel \
                libpng-devel \
                libwebp-devel \
                gcc \
                gcc-c++
        else
            print_warning "Unknown Linux distribution. You may need to install dependencies manually."
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        print_status "Detected macOS system"
        if command -v brew &> /dev/null; then
            brew install python@3.11 jpeg libpng webp
        else
            print_warning "Homebrew not found. Please install it from https://brew.sh"
        fi
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        # Windows
        print_status "Detected Windows system"
        print_warning "Please ensure you have Visual C++ Build Tools installed"
    fi
}

# Create virtual environment
create_venv() {
    print_status "Creating virtual environment..."
    
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        print_success "Virtual environment created"
    else
        print_warning "Virtual environment already exists"
    fi
    
    # Activate virtual environment
    if [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        source venv/Scripts/activate
    else
        source venv/bin/activate
    fi
    
    print_success "Virtual environment activated"
}

# Install Matrix Vision
install_matrix_vision() {
    print_status "Installing Matrix Vision..."
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install Matrix Vision
    if [ -f "pyproject.toml" ]; then
        # Development installation
        print_status "Installing in development mode..."
        pip install -e ".[dev,gui,web]"
    else
        # Production installation
        print_status "Installing from PyPI..."
        pip install matrix-vision[gui,web]
    fi
    
    print_success "Matrix Vision installed successfully"
}

# Verify installation
verify_installation() {
    print_status "Verifying installation..."
    
    if command -v matrix-vision &> /dev/null; then
        print_success "matrix-vision command is available"
        
        # Test basic functionality
        matrix-vision --version
        matrix-vision status
        
        print_success "Installation verification completed"
    else
        print_error "matrix-vision command not found"
        exit 1
    fi
}

# Create desktop shortcut (Linux/macOS)
create_shortcut() {
    if [[ "$1" == "--desktop-shortcut" ]]; then
        print_status "Creating desktop shortcut..."
        
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            DESKTOP_FILE="$HOME/Desktop/Matrix Vision.desktop"
            cat > "$DESKTOP_FILE" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=Matrix Vision
Comment=AI-powered screen streaming
Exec=$(which matrix-vision) server
Icon=applications-multimedia
Terminal=true
Categories=Multimedia;
EOF
            chmod +x "$DESKTOP_FILE"
            print_success "Desktop shortcut created"
        elif [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS - create alias
            osascript -e "tell application \"Finder\" to make alias file to POSIX file \"$(which matrix-vision)\" at desktop"
            print_success "Desktop alias created"
        fi
    fi
}

# Main installation process
main() {
    echo
    print_status "Starting Matrix Vision installation..."
    echo
    
    # Parse arguments
    DESKTOP_SHORTCUT=false
    for arg in "$@"; do
        case $arg in
            --desktop-shortcut)
                DESKTOP_SHORTCUT=true
                shift
                ;;
            --help)
                echo "Usage: $0 [--desktop-shortcut] [--help]"
                echo
                echo "Options:"
                echo "  --desktop-shortcut    Create desktop shortcut"
                echo "  --help               Show this help message"
                exit 0
                ;;
        esac
    done
    
    # Run installation steps
    check_python
    install_system_deps
    create_venv
    install_matrix_vision
    verify_installation
    
    if [ "$DESKTOP_SHORTCUT" = true ]; then
        create_shortcut --desktop-shortcut
    fi
    
    echo
    print_success "🎉 Matrix Vision installation completed!"
    echo
    print_status "Quick start:"
    echo "  1. Activate virtual environment: source venv/bin/activate"
    echo "  2. Start server: matrix-vision server"
    echo "  3. Start MCP server: matrix-vision mcp"
    echo "  4. Capture screen: matrix-vision capture --output screenshot.jpg"
    echo
    print_status "For more information, visit: https://github.com/inkbytefo/matrix-vision"
    echo
}

# Run main function
main "$@"
