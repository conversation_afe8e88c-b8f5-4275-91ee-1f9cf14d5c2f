"""AI client interface for receiving screen data streams."""

import asyncio
import json
import time
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Callable, Any

from ..core.config import settings
from ..core.exceptions import WebSocketError
from ..core.logger import LoggerMixin
from .data_encoder import EncodingFormat


@dataclass
class StreamingSession:
    """Represents an active streaming session with an AI client."""
    
    session_id: str
    client_type: str  # "claude", "gpt", "custom", etc.
    created_at: float = field(default_factory=time.perf_counter)
    last_activity: float = field(default_factory=time.perf_counter)
    
    # Streaming preferences
    encoding_format: EncodingFormat = EncodingFormat.BASE64_JPEG
    quality: int = 85
    fps: int = 10
    monitor: int = 0
    region: Optional[tuple] = None
    
    # Session statistics
    frames_sent: int = 0
    bytes_sent: int = 0
    errors: int = 0
    
    # AI-specific settings
    ai_context: Dict[str, Any] = field(default_factory=dict)
    
    def update_activity(self) -> None:
        """Update last activity timestamp."""
        self.last_activity = time.perf_counter()
    
    def get_session_duration(self) -> float:
        """Get session duration in seconds."""
        return time.perf_counter() - self.created_at
    
    def to_dict(self) -> dict:
        """Convert to dictionary for serialization."""
        return {
            "session_id": self.session_id,
            "client_type": self.client_type,
            "created_at": self.created_at,
            "last_activity": self.last_activity,
            "session_duration": self.get_session_duration(),
            "encoding_format": self.encoding_format.value,
            "quality": self.quality,
            "fps": self.fps,
            "monitor": self.monitor,
            "region": self.region,
            "frames_sent": self.frames_sent,
            "bytes_sent": self.bytes_sent,
            "errors": self.errors,
            "ai_context": self.ai_context,
        }


class AIInterface(LoggerMixin):
    """Interface for AI clients to receive screen data streams."""
    
    def __init__(self):
        self.sessions: Dict[str, StreamingSession] = {}
        self.message_handlers: Dict[str, Callable] = {
            "start_vision": self._handle_start_vision,
            "stop_vision": self._handle_stop_vision,
            "update_settings": self._handle_update_settings,
            "get_session_info": self._handle_get_session_info,
            "set_ai_context": self._handle_set_ai_context,
        }
        
        # Callbacks for integration with streaming system
        self.on_session_start: Optional[Callable] = None
        self.on_session_stop: Optional[Callable] = None
        self.on_settings_update: Optional[Callable] = None
    
    async def handle_ai_request(self, request: dict) -> dict:
        """
        Handle request from AI client.
        
        Args:
            request: Request data from AI client
            
        Returns:
            Response dictionary
        """
        try:
            action = request.get("action")
            session_id = request.get("session_id")
            
            if action not in self.message_handlers:
                return self._create_error_response(
                    f"Unknown action: {action}",
                    request_id=request.get("request_id")
                )
            
            # Handle the action
            handler = self.message_handlers[action]
            response = await handler(request)
            
            # Update session activity if session exists
            if session_id and session_id in self.sessions:
                self.sessions[session_id].update_activity()
            
            return response
            
        except Exception as e:
            self.logger.error("AI request handling error", error=str(e))
            return self._create_error_response(
                f"Internal error: {e}",
                request_id=request.get("request_id")
            )
    
    async def _handle_start_vision(self, request: dict) -> dict:
        """Handle start vision request from AI client."""
        session_id = request.get("session_id")
        client_type = request.get("client_type", "unknown")
        
        if not session_id:
            return self._create_error_response("session_id is required")
        
        if session_id in self.sessions:
            return self._create_error_response(f"Session {session_id} already exists")
        
        # Create new session
        session = StreamingSession(
            session_id=session_id,
            client_type=client_type
        )
        
        # Apply client-specific optimizations
        await self._optimize_for_client(session, client_type)
        
        # Update session settings from request
        settings_data = request.get("settings", {})
        if settings_data:
            await self._update_session_settings(session, settings_data)
        
        # Store session
        self.sessions[session_id] = session
        
        # Notify streaming system
        if self.on_session_start:
            await self.on_session_start(session)
        
        self.logger.info(
            "AI vision session started",
            session_id=session_id,
            client_type=client_type,
            settings=session.to_dict()
        )
        
        return self._create_success_response(
            "Vision session started",
            data={"session": session.to_dict()},
            request_id=request.get("request_id")
        )
    
    async def _handle_stop_vision(self, request: dict) -> dict:
        """Handle stop vision request from AI client."""
        session_id = request.get("session_id")
        
        if not session_id:
            return self._create_error_response("session_id is required")
        
        if session_id not in self.sessions:
            return self._create_error_response(f"Session {session_id} not found")
        
        session = self.sessions[session_id]
        
        # Notify streaming system
        if self.on_session_stop:
            await self.on_session_stop(session)
        
        # Remove session
        del self.sessions[session_id]
        
        self.logger.info(
            "AI vision session stopped",
            session_id=session_id,
            duration=session.get_session_duration(),
            frames_sent=session.frames_sent
        )
        
        return self._create_success_response(
            "Vision session stopped",
            data={"session_stats": session.to_dict()},
            request_id=request.get("request_id")
        )
    
    async def _handle_update_settings(self, request: dict) -> dict:
        """Handle settings update request from AI client."""
        session_id = request.get("session_id")
        settings_data = request.get("settings", {})
        
        if not session_id:
            return self._create_error_response("session_id is required")
        
        if session_id not in self.sessions:
            return self._create_error_response(f"Session {session_id} not found")
        
        if not settings_data:
            return self._create_error_response("settings data is required")
        
        session = self.sessions[session_id]
        await self._update_session_settings(session, settings_data)
        
        # Notify streaming system
        if self.on_settings_update:
            await self.on_settings_update(session)
        
        self.logger.info(
            "AI session settings updated",
            session_id=session_id,
            settings=settings_data
        )
        
        return self._create_success_response(
            "Settings updated",
            data={"session": session.to_dict()},
            request_id=request.get("request_id")
        )
    
    async def _handle_get_session_info(self, request: dict) -> dict:
        """Handle session info request from AI client."""
        session_id = request.get("session_id")
        
        if not session_id:
            return self._create_error_response("session_id is required")
        
        if session_id not in self.sessions:
            return self._create_error_response(f"Session {session_id} not found")
        
        session = self.sessions[session_id]
        
        return self._create_success_response(
            "Session info retrieved",
            data={"session": session.to_dict()},
            request_id=request.get("request_id")
        )
    
    async def _handle_set_ai_context(self, request: dict) -> dict:
        """Handle AI context setting request."""
        session_id = request.get("session_id")
        context_data = request.get("context", {})
        
        if not session_id:
            return self._create_error_response("session_id is required")
        
        if session_id not in self.sessions:
            return self._create_error_response(f"Session {session_id} not found")
        
        session = self.sessions[session_id]
        session.ai_context.update(context_data)
        
        self.logger.info(
            "AI context updated",
            session_id=session_id,
            context_keys=list(context_data.keys())
        )
        
        return self._create_success_response(
            "AI context updated",
            request_id=request.get("request_id")
        )
    
    async def _optimize_for_client(self, session: StreamingSession, client_type: str) -> None:
        """Optimize session settings for specific AI client."""
        if client_type.lower() == "claude":
            # Claude Desktop optimizations
            session.encoding_format = EncodingFormat.BASE64_JPEG
            session.quality = 75
            session.fps = 5  # Lower FPS for better processing
        elif client_type.lower() == "gpt":
            # GPT optimizations
            session.encoding_format = EncodingFormat.BASE64_PNG
            session.quality = 85
            session.fps = 10
        elif client_type.lower() == "gemini":
            # Gemini optimizations
            session.encoding_format = EncodingFormat.BASE64_WEBP
            session.quality = 80
            session.fps = 8
        else:
            # Default settings
            session.encoding_format = EncodingFormat.BASE64_JPEG
            session.quality = 85
            session.fps = 10
        
        self.logger.info(
            "Session optimized for AI client",
            client_type=client_type,
            encoding_format=session.encoding_format.value,
            quality=session.quality,
            fps=session.fps
        )
    
    async def _update_session_settings(self, session: StreamingSession, settings: dict) -> None:
        """Update session settings from request data."""
        if "encoding_format" in settings:
            try:
                session.encoding_format = EncodingFormat(settings["encoding_format"])
            except ValueError:
                self.logger.warning("Invalid encoding format", format=settings["encoding_format"])
        
        if "quality" in settings:
            session.quality = max(1, min(100, settings["quality"]))
        
        if "fps" in settings:
            session.fps = max(1, min(60, settings["fps"]))
        
        if "monitor" in settings:
            session.monitor = max(0, settings["monitor"])
        
        if "region" in settings:
            session.region = settings["region"]
    
    def _create_success_response(
        self,
        message: str,
        data: Optional[dict] = None,
        request_id: Optional[str] = None
    ) -> dict:
        """Create success response."""
        response = {
            "status": "success",
            "message": message,
            "timestamp": time.perf_counter()
        }
        
        if data:
            response["data"] = data
        
        if request_id:
            response["request_id"] = request_id
        
        return response
    
    def _create_error_response(
        self,
        error_message: str,
        request_id: Optional[str] = None
    ) -> dict:
        """Create error response."""
        response = {
            "status": "error",
            "error": error_message,
            "timestamp": time.perf_counter()
        }
        
        if request_id:
            response["request_id"] = request_id
        
        return response
    
    async def send_frame_to_session(self, session_id: str, frame_data: dict) -> bool:
        """
        Send frame data to AI session.
        
        Args:
            session_id: Target session ID
            frame_data: Frame data to send
            
        Returns:
            True if sent successfully
        """
        if session_id not in self.sessions:
            return False
        
        session = self.sessions[session_id]
        
        try:
            # Update session statistics
            session.frames_sent += 1
            if "data" in frame_data:
                session.bytes_sent += len(frame_data["data"].encode('utf-8'))
            session.update_activity()
            
            return True
            
        except Exception as e:
            session.errors += 1
            self.logger.error(
                "Failed to send frame to AI session",
                session_id=session_id,
                error=str(e)
            )
            return False
    
    def get_active_sessions(self) -> List[StreamingSession]:
        """Get list of active sessions."""
        return list(self.sessions.values())
    
    def get_stats(self) -> dict:
        """Get AI interface statistics."""
        return {
            "total_sessions": len(self.sessions),
            "sessions": {
                session_id: session.to_dict()
                for session_id, session in self.sessions.items()
            }
        }
