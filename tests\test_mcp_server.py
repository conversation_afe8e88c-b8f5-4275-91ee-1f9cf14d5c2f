"""Tests for MCP server functionality."""

import asyncio
import json
import pytest
from unittest.mock import Mock, patch, AsyncMock

from matrix_vision.mcp.mcp_server import MatrixVisionMCPServer
from matrix_vision.mcp.schemas import MCPRequest, MCPResponse, ToolCall
from matrix_vision.mcp.tools import ScreenCaptureTools, StreamingTools, SystemTools


class TestMCPServer:
    """Test MCP server functionality."""
    
    @pytest.fixture
    def mcp_server(self):
        """Create MCP server instance."""
        return MatrixVisionMCPServer()
    
    @pytest.mark.asyncio
    async def test_initialization(self, mcp_server):
        """Test MCP server initialization."""
        assert not mcp_server.initialized
        assert len(mcp_server.all_tools) > 0
        
        # Check that tools are properly registered
        assert "capture_screen" in mcp_server.all_tools
        assert "start_vision_stream" in mcp_server.all_tools
        assert "get_system_info" in mcp_server.all_tools
    
    @pytest.mark.asyncio
    async def test_handle_initialize(self, mcp_server):
        """Test initialize request handling."""
        params = {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "roots": [],
                "sampling": False
            },
            "clientInfo": {
                "name": "test-client",
                "version": "1.0.0"
            }
        }
        
        result = await mcp_server._handle_initialize(params)
        
        assert "capabilities" in result
        assert "server_info" in result
        assert result["server_info"]["name"] == "matrix-vision"
    
    @pytest.mark.asyncio
    async def test_handle_tools_list(self, mcp_server):
        """Test tools list request handling."""
        result = await mcp_server._handle_tools_list({})
        
        assert "tools" in result
        assert len(result["tools"]) > 0
        
        # Check tool structure
        tool = result["tools"][0]
        assert "name" in tool
        assert "description" in tool
        assert "inputSchema" in tool
    
    @pytest.mark.asyncio
    async def test_handle_tools_call(self, mcp_server):
        """Test tool call request handling."""
        # Mock the tool execution
        with patch.object(mcp_server.all_tools["capture_screen"], 'execute_tool', new_callable=AsyncMock) as mock_execute:
            mock_result = Mock()
            mock_result.success = True
            mock_result.result = {
                "success": True,
                "data": "base64encodeddata",
                "metadata": {"tool": "capture_screen"}
            }
            mock_execute.return_value = mock_result
            
            params = {
                "name": "capture_screen",
                "arguments": {
                    "monitor": 0,
                    "format": "base64_jpeg"
                }
            }
            
            result = await mcp_server._handle_tools_call(params)
            
            assert "content" in result
            assert "isError" in result
            assert not result["isError"]
    
    @pytest.mark.asyncio
    async def test_handle_ping(self, mcp_server):
        """Test ping request handling."""
        result = await mcp_server._handle_ping({})
        
        assert result["status"] == "ok"
        assert "timestamp" in result
        assert "server" in result
        assert "version" in result
    
    @pytest.mark.asyncio
    async def test_process_message_request(self, mcp_server):
        """Test processing MCP request message."""
        message = {
            "jsonrpc": "2.0",
            "method": "ping",
            "id": 1
        }
        
        response = await mcp_server._process_message(json.dumps(message))
        
        assert response is not None
        assert response.id == 1
        assert response.result is not None
    
    @pytest.mark.asyncio
    async def test_process_message_notification(self, mcp_server):
        """Test processing MCP notification message."""
        message = {
            "jsonrpc": "2.0",
            "method": "notifications/initialized"
        }
        
        response = await mcp_server._process_message(json.dumps(message))
        
        # Notifications don't return responses
        assert response is None
        assert mcp_server.initialized
    
    @pytest.mark.asyncio
    async def test_process_invalid_json(self, mcp_server):
        """Test processing invalid JSON."""
        response = await mcp_server._process_message("invalid json")
        
        assert response is not None
        assert response.error is not None
        assert response.error.code == -32700  # Parse error
    
    @pytest.mark.asyncio
    async def test_process_unknown_method(self, mcp_server):
        """Test processing unknown method."""
        message = {
            "jsonrpc": "2.0",
            "method": "unknown_method",
            "id": 1
        }
        
        response = await mcp_server._process_message(json.dumps(message))
        
        assert response is not None
        assert response.error is not None
        assert response.error.code == -32601  # Method not found


class TestScreenCaptureTools:
    """Test screen capture tools."""
    
    @pytest.fixture
    def screen_tools(self):
        """Create screen capture tools instance."""
        return ScreenCaptureTools()
    
    def test_tools_registration(self, screen_tools):
        """Test that tools are properly registered."""
        tools = screen_tools.get_tools()
        tool_names = [tool.name for tool in tools]
        
        assert "capture_screen" in tool_names
        assert "get_monitors" in tool_names
        assert "capture_region" in tool_names
    
    @pytest.mark.asyncio
    async def test_capture_screen_tool(self, screen_tools):
        """Test capture screen tool execution."""
        # Mock the screen capture
        with patch.object(screen_tools.screen_capture, 'capture_frame', new_callable=AsyncMock) as mock_capture:
            with patch.object(screen_tools.data_encoder, 'encode_frame', new_callable=AsyncMock) as mock_encode:
                import numpy as np
                
                mock_frame = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
                mock_metadata = {"capture_time": 123456.789}
                mock_capture.return_value = (mock_frame, mock_metadata)
                
                mock_encoded = "base64encodeddata"
                mock_encoding_metadata = {"encoding_time_ms": 10.5}
                mock_encode.return_value = (mock_encoded, mock_encoding_metadata)
                
                tool_call = ToolCall(
                    tool="capture_screen",
                    parameters={"monitor": 0, "format": "base64_jpeg"}
                )
                
                result = await screen_tools.execute_tool(tool_call)
                
                assert result.success
                assert result.result["success"]
                assert result.result["data"] == mock_encoded
    
    @pytest.mark.asyncio
    async def test_get_monitors_tool(self, screen_tools):
        """Test get monitors tool execution."""
        with patch.object(screen_tools.screen_capture, 'get_monitors', new_callable=AsyncMock) as mock_monitors:
            mock_monitors.return_value = [
                {"width": 1920, "height": 1080, "left": 0, "top": 0}
            ]
            
            tool_call = ToolCall(tool="get_monitors")
            result = await screen_tools.execute_tool(tool_call)
            
            assert result.success
            assert result.result["success"]
            assert len(result.result["monitors"]) == 1


class TestStreamingTools:
    """Test streaming tools."""
    
    @pytest.fixture
    def streaming_tools(self):
        """Create streaming tools instance."""
        return StreamingTools()
    
    def test_tools_registration(self, streaming_tools):
        """Test that streaming tools are properly registered."""
        tools = streaming_tools.get_tools()
        tool_names = [tool.name for tool in tools]
        
        assert "start_vision_stream" in tool_names
        assert "stop_vision_stream" in tool_names
        assert "get_stream_status" in tool_names
    
    @pytest.mark.asyncio
    async def test_start_vision_stream_tool(self, streaming_tools):
        """Test start vision stream tool execution."""
        with patch.object(streaming_tools.ai_interface, 'handle_ai_request', new_callable=AsyncMock) as mock_handle:
            mock_handle.return_value = {
                "status": "success",
                "data": {"session_id": "test-session"}
            }
            
            tool_call = ToolCall(
                tool="start_vision_stream",
                parameters={
                    "session_id": "test-session",
                    "client_type": "claude",
                    "fps": 5
                }
            )
            
            result = await streaming_tools.execute_tool(tool_call)
            
            assert result.success
            assert result.result["success"]
            assert result.result["session_id"] == "test-session"


class TestSystemTools:
    """Test system tools."""
    
    @pytest.fixture
    def system_tools(self):
        """Create system tools instance."""
        return SystemTools()
    
    def test_tools_registration(self, system_tools):
        """Test that system tools are properly registered."""
        tools = system_tools.get_tools()
        tool_names = [tool.name for tool in tools]
        
        assert "get_system_info" in tool_names
    
    @pytest.mark.asyncio
    async def test_get_system_info_tool(self, system_tools):
        """Test get system info tool execution."""
        tool_call = ToolCall(
            tool="get_system_info",
            parameters={
                "include_monitors": True,
                "include_performance": False
            }
        )
        
        result = await system_tools.execute_tool(tool_call)
        
        assert result.success
        assert result.result["success"]
        assert "system_info" in result.result
        assert "platform" in result.result["system_info"]


@pytest.mark.integration
class TestMCPIntegration:
    """Integration tests for MCP server."""
    
    @pytest.mark.asyncio
    async def test_full_mcp_workflow(self):
        """Test full MCP workflow."""
        server = MatrixVisionMCPServer()
        
        try:
            # Initialize
            init_params = {
                "protocolVersion": "2024-11-05",
                "capabilities": {"roots": []},
                "clientInfo": {"name": "test", "version": "1.0"}
            }
            
            init_result = await server._handle_initialize(init_params)
            assert "capabilities" in init_result
            
            # List tools
            tools_result = await server._handle_tools_list({})
            assert len(tools_result["tools"]) > 0
            
            # Test a simple tool call (ping equivalent)
            ping_result = await server._handle_ping({})
            assert ping_result["status"] == "ok"
            
        except Exception as e:
            pytest.skip(f"Integration test failed: {e}")


if __name__ == "__main__":
    pytest.main([__file__])
